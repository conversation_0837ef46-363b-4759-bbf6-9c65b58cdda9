#!/bin/bash

# محلل العملات الحلال - Binance Halal Crypto Analyzer
# ملف تشغيل للينكس وماك

echo "========================================"
echo "محلل العملات الحلال"
echo "Binance Halal Crypto Analyzer"
echo "========================================"
echo

# التحقق من وجود Python
if ! command -v python3 &> /dev/null; then
    if ! command -v python &> /dev/null; then
        echo "❌ خطأ: Python غير مثبت"
        echo "يرجى تثبيت Python 3.8 أو أحدث"
        exit 1
    else
        PYTHON_CMD="python"
    fi
else
    PYTHON_CMD="python3"
fi

echo "✅ تم العثور على Python"

# التحقق من إصدار Python
PYTHON_VERSION=$($PYTHON_CMD -c "import sys; print(f'{sys.version_info.major}.{sys.version_info.minor}')")
REQUIRED_VERSION="3.8"

if [ "$(printf '%s\n' "$REQUIRED_VERSION" "$PYTHON_VERSION" | sort -V | head -n1)" != "$REQUIRED_VERSION" ]; then
    echo "❌ خطأ: يتطلب Python 3.8 أو أحدث"
    echo "الإصدار الحالي: $PYTHON_VERSION"
    exit 1
fi

echo "✅ إصدار Python مناسب: $PYTHON_VERSION"
echo

# التحقق من وجود pip
if ! $PYTHON_CMD -m pip --version &> /dev/null; then
    echo "❌ خطأ: pip غير متاح"
    exit 1
fi

echo "✅ pip متاح"
echo

# التحقق من وجود ملف المتطلبات
if [ ! -f "requirements.txt" ]; then
    echo "❌ خطأ: ملف requirements.txt غير موجود"
    exit 1
fi

# تثبيت المتطلبات
echo "🔄 جاري تثبيت المتطلبات..."
$PYTHON_CMD -m pip install -r requirements.txt

if [ $? -ne 0 ]; then
    echo
    echo "❌ فشل في تثبيت بعض المتطلبات"
    echo "يرجى التحقق من اتصال الإنترنت وإعادة المحاولة"
    exit 1
fi

echo
echo "✅ تم تثبيت المتطلبات بنجاح"
echo

# إنشاء المجلدات المطلوبة
mkdir -p data logs charts exports
echo "📁 تم إنشاء المجلدات المطلوبة"
echo

# تشغيل التطبيق
echo "🚀 تشغيل التطبيق..."
echo

$PYTHON_CMD main.py

if [ $? -ne 0 ]; then
    echo
    echo "❌ حدث خطأ أثناء تشغيل التطبيق"
    read -p "اضغط Enter للمتابعة..."
fi

echo
echo "تم إغلاق التطبيق"
