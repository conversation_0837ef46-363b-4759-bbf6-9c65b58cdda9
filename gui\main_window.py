# -*- coding: utf-8 -*-
"""
النافذة الرئيسية لتطبيق محلل العملات الحلال
"""

import sys
from PyQt6.QtWidgets import (QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, 
                             QTableWidget, QTableWidgetItem, QPushButton, 
                             QLabel, QComboBox, QProgressBar, QStatusBar,
                             QMenuBar, QMenu, QMessageBox, QSplitter,
                             QGroupBox, QGridLayout, QTextEdit, QFrame)
from PyQt6.QtCore import QTimer, QThread, pyqtSignal, Qt
from PyQt6.QtGui import QFont, QIcon, QPixmap, QAction
import pandas as pd
from datetime import datetime
import config
from api.binance_client import BinanceClient
from analysis.technical_analysis import TechnicalAnalyzer
from database.db_manager import DatabaseManager
from utils.halal_coins import get_all_halal_coins, get_halal_symbols
from gui.analysis_window import AnalysisWindow

class DataUpdateThread(QThread):
    """خيط تحديث البيانات في الخلفية"""
    
    data_updated = pyqtSignal(dict)
    error_occurred = pyqtSignal(str)
    
    def __init__(self, binance_client, analyzer, symbols):
        super().__init__()
        self.binance_client = binance_client
        self.analyzer = analyzer
        self.symbols = symbols
        self.running = True
    
    def run(self):
        """تشغيل خيط التحديث"""
        try:
            results = {}
            for symbol in self.symbols:
                if not self.running:
                    break
                    
                # جلب البيانات
                ticker_data = self.binance_client.get_24hr_ticker(symbol)
                klines_data = self.binance_client.get_klines(symbol, '1h', 100)
                
                if not klines_data.empty:
                    # التحليل الفني
                    analysis = self.analyzer.comprehensive_analysis(klines_data)
                    
                    # دمج البيانات
                    results[symbol] = {
                        'ticker': ticker_data,
                        'analysis': analysis
                    }
            
            self.data_updated.emit(results)
            
        except Exception as e:
            self.error_occurred.emit(str(e))
    
    def stop(self):
        """إيقاف الخيط"""
        self.running = False

class MainWindow(QMainWindow):
    """النافذة الرئيسية للتطبيق"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("محلل العملات الحلال - Binance Halal Crypto Analyzer")
        self.setGeometry(100, 100, config.WINDOW_WIDTH, config.WINDOW_HEIGHT)
        
        # تهيئة المكونات
        self.binance_client = BinanceClient()
        self.analyzer = TechnicalAnalyzer()
        self.db_manager = DatabaseManager()
        self.halal_coins = get_all_halal_coins()
        self.symbols = get_halal_symbols()
        
        # متغيرات التحديث
        self.update_thread = None
        self.auto_update_timer = QTimer()
        self.auto_update_timer.timeout.connect(self.update_data)
        
        # إعداد الواجهة
        self.setup_ui()
        self.setup_menu()
        self.setup_status_bar()
        
        # تحديث أولي
        self.update_data()
        
        # بدء التحديث التلقائي
        self.auto_update_timer.start(config.UPDATE_INTERVAL * 1000)
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout(central_widget)
        
        # شريط الأدوات العلوي
        toolbar_layout = QHBoxLayout()
        
        # أزرار التحكم
        self.refresh_btn = QPushButton("تحديث الكل")
        self.refresh_btn.clicked.connect(self.update_data)
        
        self.buy_signals_btn = QPushButton("إشارات الشراء فقط")
        self.buy_signals_btn.clicked.connect(self.filter_buy_signals)
        
        self.auto_update_btn = QPushButton("إيقاف التحديث التلقائي")
        self.auto_update_btn.clicked.connect(self.toggle_auto_update)
        
        # شريط التقدم
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        
        toolbar_layout.addWidget(self.refresh_btn)
        toolbar_layout.addWidget(self.buy_signals_btn)
        toolbar_layout.addWidget(self.auto_update_btn)
        toolbar_layout.addStretch()
        toolbar_layout.addWidget(self.progress_bar)
        
        main_layout.addLayout(toolbar_layout)
        
        # الجدول الرئيسي
        self.setup_main_table()
        main_layout.addWidget(self.table)
        
        # معلومات إضافية
        info_layout = QHBoxLayout()
        
        # مجموعة الإحصائيات
        stats_group = QGroupBox("إحصائيات السوق")
        stats_layout = QGridLayout(stats_group)
        
        self.total_coins_label = QLabel("إجمالي العملات: 0")
        self.buy_signals_label = QLabel("إشارات شراء: 0")
        self.sell_signals_label = QLabel("إشارات بيع: 0")
        self.last_update_label = QLabel("آخر تحديث: --")
        
        stats_layout.addWidget(self.total_coins_label, 0, 0)
        stats_layout.addWidget(self.buy_signals_label, 0, 1)
        stats_layout.addWidget(self.sell_signals_label, 1, 0)
        stats_layout.addWidget(self.last_update_label, 1, 1)
        
        info_layout.addWidget(stats_group)
        
        # مجموعة الإشعارات
        notifications_group = QGroupBox("الإشعارات الأخيرة")
        notifications_layout = QVBoxLayout(notifications_group)
        
        self.notifications_text = QTextEdit()
        self.notifications_text.setMaximumHeight(100)
        self.notifications_text.setReadOnly(True)
        
        notifications_layout.addWidget(self.notifications_text)
        
        info_layout.addWidget(notifications_group)
        
        main_layout.addLayout(info_layout)
    
    def setup_main_table(self):
        """إعداد الجدول الرئيسي"""
        self.table = QTableWidget()
        
        # أعمدة الجدول
        headers = [
            "العملة", "الاسم العربي", "السعر الحالي", "التغيير %", 
            "الحجم", "RSI", "MACD", "الاتجاه", "التوصية", "الدعم", 
            "المقاومة", "آخر تحديث", "تفاصيل"
        ]
        
        self.table.setColumnCount(len(headers))
        self.table.setHorizontalHeaderLabels(headers)
        
        # تنسيق الجدول
        self.table.setAlternatingRowColors(True)
        self.table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        self.table.setSortingEnabled(True)
        
        # ضبط عرض الأعمدة
        header = self.table.horizontalHeader()
        header.setStretchLastSection(True)
        
        # الاتصال بالأحداث
        self.table.cellDoubleClicked.connect(self.show_detailed_analysis)
    
    def setup_menu(self):
        """إعداد شريط القوائم"""
        menubar = self.menuBar()
        
        # قائمة الملف
        file_menu = menubar.addMenu("ملف")
        
        export_action = QAction("تصدير البيانات", self)
        export_action.triggered.connect(self.export_data)
        file_menu.addAction(export_action)
        
        file_menu.addSeparator()
        
        exit_action = QAction("خروج", self)
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)
        
        # قائمة العرض
        view_menu = menubar.addMenu("عرض")
        
        refresh_action = QAction("تحديث", self)
        refresh_action.triggered.connect(self.update_data)
        view_menu.addAction(refresh_action)
        
        # قائمة المساعدة
        help_menu = menubar.addMenu("مساعدة")
        
        about_action = QAction("حول", self)
        about_action.triggered.connect(self.show_about)
        help_menu.addAction(about_action)
    
    def setup_status_bar(self):
        """إعداد شريط الحالة"""
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)
        
        # تسمية حالة الاتصال
        self.connection_label = QLabel("غير متصل")
        self.status_bar.addPermanentWidget(self.connection_label)
        
        self.update_connection_status()
    
    def update_connection_status(self):
        """تحديث حالة الاتصال"""
        if self.binance_client.is_connected():
            self.connection_label.setText("متصل بـ Binance")
            self.connection_label.setStyleSheet("color: green")
        else:
            self.connection_label.setText("غير متصل")
            self.connection_label.setStyleSheet("color: red")
    
    def update_data(self):
        """تحديث البيانات"""
        if self.update_thread and self.update_thread.isRunning():
            return
        
        self.progress_bar.setVisible(True)
        self.progress_bar.setRange(0, 0)  # شريط تقدم غير محدد
        self.status_bar.showMessage("جاري تحديث البيانات...")
        
        # إنشاء خيط التحديث
        self.update_thread = DataUpdateThread(
            self.binance_client, 
            self.analyzer, 
            self.symbols
        )
        
        self.update_thread.data_updated.connect(self.on_data_updated)
        self.update_thread.error_occurred.connect(self.on_error_occurred)
        self.update_thread.finished.connect(self.on_update_finished)
        
        self.update_thread.start()
    
    def on_data_updated(self, results):
        """معالج تحديث البيانات"""
        self.populate_table(results)
        self.update_statistics(results)
        self.update_notifications()
        
        # حفظ البيانات في قاعدة البيانات
        for symbol, data in results.items():
            if 'analysis' in data:
                self.db_manager.save_analysis(symbol, data['analysis'])
    
    def on_error_occurred(self, error_message):
        """معالج الأخطاء"""
        QMessageBox.warning(self, "خطأ", f"حدث خطأ أثناء تحديث البيانات:\n{error_message}")
    
    def on_update_finished(self):
        """معالج انتهاء التحديث"""
        self.progress_bar.setVisible(False)
        self.status_bar.showMessage("تم التحديث بنجاح", 3000)
        self.last_update_label.setText(f"آخر تحديث: {datetime.now().strftime('%H:%M:%S')}")
        self.update_connection_status()
    
    def populate_table(self, results):
        """ملء الجدول بالبيانات"""
        self.table.setRowCount(len(results))
        
        row = 0
        for symbol, data in results.items():
            coin_info = self.halal_coins.get(symbol, {})
            ticker = data.get('ticker', {})
            analysis = data.get('analysis', {})
            
            # العملة
            self.table.setItem(row, 0, QTableWidgetItem(coin_info.get('symbol', symbol)))
            
            # الاسم العربي
            self.table.setItem(row, 1, QTableWidgetItem(coin_info.get('arabic_name', '')))
            
            # السعر الحالي
            price = ticker.get('price', 0)
            self.table.setItem(row, 2, QTableWidgetItem(f"${price:.4f}"))
            
            # التغيير %
            change_percent = ticker.get('change_percent', 0)
            change_item = QTableWidgetItem(f"{change_percent:.2f}%")
            if change_percent > 0:
                change_item.setBackground(Qt.GlobalColor.green)
            elif change_percent < 0:
                change_item.setBackground(Qt.GlobalColor.red)
            self.table.setItem(row, 3, change_item)
            
            # الحجم
            volume = ticker.get('volume', 0)
            self.table.setItem(row, 4, QTableWidgetItem(f"{volume:,.0f}"))
            
            # RSI
            rsi = analysis.get('rsi', 0)
            self.table.setItem(row, 5, QTableWidgetItem(f"{rsi:.1f}"))
            
            # MACD
            macd = analysis.get('macd', {}).get('macd', 0)
            self.table.setItem(row, 6, QTableWidgetItem(f"{macd:.4f}"))
            
            # الاتجاه
            trend = analysis.get('trend', 'غير محدد')
            self.table.setItem(row, 7, QTableWidgetItem(trend))
            
            # التوصية
            signal = analysis.get('signals', {}).get('overall_signal', 'محايد')
            signal_item = QTableWidgetItem(signal)
            if signal == 'شراء':
                signal_item.setBackground(Qt.GlobalColor.green)
            elif signal == 'بيع':
                signal_item.setBackground(Qt.GlobalColor.red)
            self.table.setItem(row, 8, signal_item)
            
            # الدعم
            support = analysis.get('support_resistance', {}).get('support', 0)
            self.table.setItem(row, 9, QTableWidgetItem(f"${support:.4f}"))
            
            # المقاومة
            resistance = analysis.get('support_resistance', {}).get('resistance', 0)
            self.table.setItem(row, 10, QTableWidgetItem(f"${resistance:.4f}"))
            
            # آخر تحديث
            self.table.setItem(row, 11, QTableWidgetItem(datetime.now().strftime('%H:%M:%S')))
            
            # زر التفاصيل
            details_btn = QPushButton("عرض التفاصيل")
            details_btn.clicked.connect(lambda checked, s=symbol: self.show_detailed_analysis_for_symbol(s))
            self.table.setCellWidget(row, 12, details_btn)
            
            row += 1
    
    def update_statistics(self, results):
        """تحديث الإحصائيات"""
        total_coins = len(results)
        buy_signals = sum(1 for data in results.values() 
                         if data.get('analysis', {}).get('signals', {}).get('overall_signal') == 'شراء')
        sell_signals = sum(1 for data in results.values() 
                          if data.get('analysis', {}).get('signals', {}).get('overall_signal') == 'بيع')
        
        self.total_coins_label.setText(f"إجمالي العملات: {total_coins}")
        self.buy_signals_label.setText(f"إشارات شراء: {buy_signals}")
        self.sell_signals_label.setText(f"إشارات بيع: {sell_signals}")
    
    def update_notifications(self):
        """تحديث الإشعارات"""
        notifications = self.db_manager.get_unread_notifications()
        
        if notifications:
            text = "\n".join([f"• {notif['message']}" for notif in notifications[-5:]])
            self.notifications_text.setText(text)
    
    def filter_buy_signals(self):
        """فلترة إشارات الشراء فقط"""
        for row in range(self.table.rowCount()):
            signal_item = self.table.item(row, 8)
            if signal_item and signal_item.text() != 'شراء':
                self.table.hideRow(row)
            else:
                self.table.showRow(row)
    
    def toggle_auto_update(self):
        """تبديل التحديث التلقائي"""
        if self.auto_update_timer.isActive():
            self.auto_update_timer.stop()
            self.auto_update_btn.setText("تشغيل التحديث التلقائي")
        else:
            self.auto_update_timer.start(config.UPDATE_INTERVAL * 1000)
            self.auto_update_btn.setText("إيقاف التحديث التلقائي")
    
    def show_detailed_analysis(self, row, column):
        """عرض التحليل المفصل عند النقر المزدوج"""
        symbol_item = self.table.item(row, 0)
        if symbol_item:
            symbol = symbol_item.text() + 'USDT'
            self.show_detailed_analysis_for_symbol(symbol)
    
    def show_detailed_analysis_for_symbol(self, symbol):
        """عرض التحليل المفصل لعملة محددة"""
        analysis_window = AnalysisWindow(symbol, self.binance_client, self.analyzer)
        analysis_window.exec()
    
    def export_data(self):
        """تصدير البيانات"""
        # TODO: تنفيذ تصدير البيانات
        QMessageBox.information(self, "تصدير", "سيتم تنفيذ هذه الميزة قريباً")
    
    def show_about(self):
        """عرض معلومات حول التطبيق"""
        QMessageBox.about(self, "حول التطبيق", 
                         "محلل العملات الحلال - Binance Halal Crypto Analyzer\n\n"
                         "تطبيق لتحليل العملات الرقمية الحلال المدرجة في منصة Binance\n"
                         "يوفر التحليل الفني والتوصيات الذكية\n\n"
                         "المطور: AI Assistant\n"
                         "الإصدار: 1.0")
    
    def closeEvent(self, event):
        """معالج إغلاق التطبيق"""
        if self.update_thread and self.update_thread.isRunning():
            self.update_thread.stop()
            self.update_thread.wait()
        
        event.accept()
