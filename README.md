# محلل العملات الحلال - Binance Halal Crypto Analyzer

## نظرة عامة

تطبيق سطح مكتب احترافي مطور بلغة Python لتحليل العملات الرقمية الحلال المدرجة في منصة Binance. يقوم التطبيق بجلب البيانات الحقيقية مباشرة من واجهة برمجة التطبيقات الخاصة بـ Binance، ثم تحليل كل عملة بناءً على مؤشرات فنية متقدمة مع توصيات ذكية.

## المميزات الرئيسية

### 🔍 التحليل الفني الشامل
- **مؤشر القوة النسبية (RSI)**: لتحديد مناطق التشبع الشرائي والبيعي
- **مؤشر MACD**: لتحليل الاتجاهات والزخم
- **المتوسطات المتحركة**: للتنبؤ بالاتجاهات قصيرة وطويلة المدى
- **نطاقات بولينجر**: لقياس التقلبات
- **مؤشرات الحجم**: لتأكيد الاتجاهات

### 📊 واجهة مستخدم متقدمة
- **جدول تفاعلي**: عرض جميع العملات مع معلوماتها الأساسية
- **رسوم بيانية**: شموع يابانية مع المؤشرات الفنية
- **تحديث مباشر**: كل دقيقة للأسعار والتحليلات
- **فلترة ذكية**: عرض إشارات الشراء/البيع فقط
- **دعم اللغة العربية**: واجهة مستخدم باللغة العربية

### 🔔 نظام الإشعارات
- **إشعارات فورية**: عند ظهور إشارات شراء أو بيع جديدة
- **تنبيهات الأسعار**: عند الوصول لمستويات محددة
- **إشعارات الحجم**: عند ارتفاع حجم التداول بشكل غير طبيعي
- **أيقونة النظام**: للوصول السريع والإشعارات

### 📈 التوصيات الذكية
- **توصيات شراء/بيع**: بناءً على تحليل متعدد المؤشرات
- **نقاط الدخول والخروج**: اقتراحات للأسعار المثلى
- **مستويات الدعم والمقاومة**: لإدارة المخاطر
- **تحليل الاتجاهات**: صاعد/هابط/جانبي

### 💾 إدارة البيانات
- **قاعدة بيانات SQLite**: لحفظ التحليلات التاريخية
- **تصدير متعدد الصيغ**: Excel, PDF, CSV, JSON
- **تقارير مفصلة**: إحصائيات وتحليلات شاملة
- **أرشفة البيانات**: للمراجعة والمقارنة

## العملات المدعومة

التطبيق يدعم العملات الرقمية الحلال التالية:

| العملة | الرمز | الاسم العربي | الفئة |
|---------|-------|-------------|-------|
| Bitcoin | BTC | بيتكوين | عملة رقمية أساسية |
| Ethereum | ETH | إيثيريوم | منصة عقود ذكية |
| Cardano | ADA | كاردانو | منصة بلوك تشين |
| Polkadot | DOT | بولكادوت | شبكة متعددة السلاسل |
| Chainlink | LINK | تشين لينك | شبكة أوراكل |
| Litecoin | LTC | لايتكوين | عملة رقمية |
| Stellar | XLM | ستيلار | شبكة دفع |
| VeChain | VET | في تشين | سلسلة التوريد |
| Algorand | ALGO | الجوراند | منصة بلوك تشين |
| Cosmos | ATOM | كوزموس | شبكة بلوك تشين |
| Tezos | XTZ | تيزوس | منصة عقود ذكية |
| Filecoin | FIL | فايل كوين | تخزين لامركزي |
| Internet Computer | ICP | إنترنت كمبيوتر | حوسبة لامركزية |
| NEAR Protocol | NEAR | نير بروتوكول | منصة تطوير |
| Solana | SOL | سولانا | منصة عقود ذكية |

## متطلبات النظام

### متطلبات الأجهزة
- **المعالج**: Intel Core i3 أو AMD Ryzen 3 أو أحدث
- **الذاكرة**: 4 GB RAM كحد أدنى، 8 GB مُوصى به
- **التخزين**: 500 MB مساحة فارغة
- **الشاشة**: دقة 1024x768 كحد أدنى، 1920x1080 مُوصى به

### متطلبات البرمجيات
- **نظام التشغيل**: Windows 10/11, macOS 10.14+, Linux Ubuntu 18.04+
- **Python**: الإصدار 3.8 أو أحدث
- **اتصال الإنترنت**: مطلوب للحصول على البيانات المباشرة

## التثبيت والإعداد

### 1. تحميل المشروع
```bash
git clone https://github.com/your-repo/binance-halal-crypto-analyzer.git
cd binance-halal-crypto-analyzer
```

### 2. إنشاء بيئة افتراضية
```bash
python -m venv venv

# Windows
venv\Scripts\activate

# macOS/Linux
source venv/bin/activate
```

### 3. تثبيت المتطلبات
```bash
pip install -r requirements.txt
```

### 4. إعداد متغيرات البيئة (اختياري)
إنشاء ملف `.env` في المجلد الجذر:
```env
BINANCE_API_KEY=your_api_key_here
BINANCE_API_SECRET=your_api_secret_here
```

**ملاحظة**: مفاتيح API اختيارية للبيانات العامة، لكنها مطلوبة للميزات المتقدمة.

### 5. تشغيل التطبيق
```bash
python main.py
```

## كيفية الاستخدام

### الواجهة الرئيسية
1. **عرض العملات**: جدول يعرض جميع العملات الحلال مع معلوماتها
2. **التحديث**: انقر على "تحديث الكل" لجلب أحدث البيانات
3. **الفلترة**: استخدم "إشارات الشراء فقط" لعرض الفرص الاستثمارية
4. **التفاصيل**: انقر مزدوجاً على أي عملة لعرض التحليل المفصل

### نافذة التحليل المفصل
- **الرسم البياني**: شموع يابانية مع المؤشرات الفنية
- **المؤشرات**: جدول مفصل بجميع المؤشرات وتفسيرها
- **التوصيات**: توصيات شراء/بيع مع نقاط الدخول والخروج

### التصدير والتقارير
1. انتقل إلى قائمة "ملف" > "تصدير البيانات"
2. اختر الصيغة المطلوبة (Excel, PDF, CSV, JSON)
3. سيتم حفظ الملف في مجلد `exports`

### إعدادات الإشعارات
- **تفعيل الإشعارات**: من خلال أيقونة النظام
- **تخصيص التنبيهات**: إعداد حدود الأسعار والحجم
- **إدارة الإشعارات**: عرض وإدارة الإشعارات السابقة

## الهيكل التقني

```
binance-halal-crypto-analyzer/
├── main.py                 # نقطة دخول التطبيق
├── config.py              # إعدادات التطبيق
├── requirements.txt       # متطلبات Python
├── README.md             # هذا الملف
├── api/                  # وحدات API
│   ├── __init__.py
│   └── binance_client.py # عميل Binance API
├── analysis/             # وحدات التحليل
│   ├── __init__.py
│   └── technical_analysis.py # التحليل الفني
├── database/             # قاعدة البيانات
│   ├── __init__.py
│   └── db_manager.py     # مدير قاعدة البيانات
├── gui/                  # واجهة المستخدم
│   ├── __init__.py
│   ├── main_window.py    # النافذة الرئيسية
│   └── analysis_window.py # نافذة التحليل
├── utils/                # أدوات مساعدة
│   ├── __init__.py
│   ├── halal_coins.py    # قائمة العملات الحلال
│   ├── notifications.py  # نظام الإشعارات
│   └── export_manager.py # مدير التصدير
├── data/                 # بيانات التطبيق
├── logs/                 # ملفات السجل
├── charts/               # الرسوم البيانية المحفوظة
└── exports/              # الملفات المُصدَّرة
```

## المساهمة في المشروع

نرحب بالمساهمات! يرجى اتباع الخطوات التالية:

1. Fork المشروع
2. إنشاء فرع للميزة الجديدة (`git checkout -b feature/AmazingFeature`)
3. Commit التغييرات (`git commit -m 'Add some AmazingFeature'`)
4. Push للفرع (`git push origin feature/AmazingFeature`)
5. فتح Pull Request

## الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

## إخلاء المسؤولية

⚠️ **تنويه مهم**: هذا التطبيق للأغراض التعليمية والبحثية فقط. لا يُعتبر نصيحة استثمارية أو مالية. العملات الرقمية استثمارات عالية المخاطر. يُرجى إجراء البحث الخاص بك واستشارة مستشار مالي مؤهل قبل اتخاذ أي قرارات استثمارية.

## الدعم والمساعدة

- **البريد الإلكتروني**: <EMAIL>
- **المشاكل**: [GitHub Issues](https://github.com/your-repo/issues)
- **الوثائق**: [Wiki](https://github.com/your-repo/wiki)

## تحديثات المشروع

- **الإصدار 1.0**: الإصدار الأولي مع الميزات الأساسية
- **الإصدار 1.1**: إضافة نظام الإشعارات والتصدير
- **الإصدار 1.2**: تحسينات الأداء ودعم المزيد من العملات

---

**تم تطوير هذا المشروع بواسطة AI Assistant - 2025**
