#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
محلل العملات الحلال - Binance Halal Crypto Analyzer
إصدار PyQt5 المتوافق
"""

import sys
import os

# محاولة استيراد PyQt5
try:
    from PyQt5.QtWidgets import QApplication
    from PyQt5.QtCore import QTranslator, QLocale
    from gui.main_window_pyqt5 import MainWindow
    QT_VERSION = "PyQt5"
    print("✅ استخدام PyQt5")
except ImportError:
    try:
        from PyQt6.QtWidgets import QApplication
        from PyQt6.QtCore import QTranslator, QLocale
        from gui.main_window import MainWindow
        QT_VERSION = "PyQt6"
        print("✅ استخدام PyQt6")
    except ImportError:
        try:
            from PySide6.QtWidgets import QApplication
            from PySide6.QtCore import QTranslator, QLocale
            from gui.main_window_pyside6 import MainWindow
            QT_VERSION = "PySide6"
            print("✅ استخدام PySide6")
        except ImportError:
            print("❌ خطأ: لم يتم العثور على أي مكتبة Qt")
            print("يرجى تثبيت إحدى المكتبات التالية:")
            print("- pip install PyQt5")
            print("- pip install PyQt6")
            print("- pip install PySide6")
            input("اضغط Enter للخروج...")
            sys.exit(1)

def main():
    """دالة تشغيل التطبيق الرئيسية"""
    print(f"🚀 تشغيل محلل العملات الحلال باستخدام {QT_VERSION}")
    
    app = QApplication(sys.argv)
    
    # إعداد اللغة العربية
    app.setLayoutDirection(app.layoutDirection())
    
    # إنشاء النافذة الرئيسية
    try:
        window = MainWindow()
        window.show()
        
        print("✅ تم تشغيل التطبيق بنجاح")
        
        # تشغيل التطبيق
        sys.exit(app.exec_() if QT_VERSION == "PyQt5" else app.exec())
        
    except Exception as e:
        print(f"❌ خطأ في تشغيل التطبيق: {e}")
        print("يرجى تشغيل fix_pyqt.py لإصلاح المشاكل")
        input("اضغط Enter للخروج...")
        sys.exit(1)

if __name__ == "__main__":
    main()
