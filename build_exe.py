#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
سكريبت بناء ملف EXE للتطبيق
يستخدم PyInstaller لإنشاء ملف تنفيذي مستقل
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def check_pyinstaller():
    """التحقق من وجود PyInstaller"""
    try:
        import PyInstaller
        print("✅ PyInstaller متاح")
        return True
    except ImportError:
        print("❌ PyInstaller غير متاح")
        print("جاري تثبيت PyInstaller...")
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", "pyinstaller"])
            print("✅ تم تثبيت PyInstaller بنجاح")
            return True
        except subprocess.CalledProcessError:
            print("❌ فشل في تثبيت PyInstaller")
            return False

def create_spec_file():
    """إنشاء ملف .spec لـ PyInstaller"""
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

# قائمة الملفات الإضافية المطلوبة
added_files = [
    ('config.py', '.'),
    ('README.md', '.'),
    ('QUICK_START.md', '.'),
    ('LICENSE', '.'),
]

# قائمة المجلدات المطلوبة
added_folders = [
    ('api', 'api'),
    ('analysis', 'analysis'),
    ('database', 'database'),
    ('gui', 'gui'),
    ('utils', 'utils'),
]

a = Analysis(
    ['main.py'],
    pathex=[],
    binaries=[],
    datas=added_files + added_folders,
    hiddenimports=[
        'PyQt6.QtCore',
        'PyQt6.QtGui',
        'PyQt6.QtWidgets',
        'PyQt6.QtSvg',
        'pandas',
        'numpy',
        'matplotlib',
        'matplotlib.backends.backend_qt5agg',
        'matplotlib.figure',
        'requests',
        'ta',
        'ta.momentum',
        'ta.trend',
        'ta.volatility',
        'ta.volume',
        'sqlite3',
        'reportlab',
        'reportlab.lib',
        'reportlab.platypus',
        'openpyxl',
        'binance',
        'binance.client',
        'binance.exceptions',
        'dateutil',
        'pytz',
        'plotly',
        'mplfinance',
        'PIL',
        'PIL.Image',
        'PIL.ImageDraw',
        'json',
        'logging',
        'threading',
        'queue',
    ],
    hookspath=['pyinstaller_hooks'],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[
        'tkinter',
        'test',
        'unittest',
        'pydoc',
        'doctest',
    ],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='HalalCryptoAnalyzer',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,  # إخفاء نافذة الكونسول
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='icon.ico' if os.path.exists('icon.ico') else None,
    version='version_info.txt' if os.path.exists('version_info.txt') else None,
)
'''
    
    with open('HalalCryptoAnalyzer.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content)
    
    print("✅ تم إنشاء ملف .spec")

def create_version_info():
    """إنشاء ملف معلومات الإصدار"""
    version_info = '''# UTF-8
#
# For more details about fixed file info 'ffi' see:
# http://msdn.microsoft.com/en-us/library/ms646997.aspx
VSVersionInfo(
  ffi=FixedFileInfo(
    filevers=(1,0,0,0),
    prodvers=(1,0,0,0),
    mask=0x3f,
    flags=0x0,
    OS=0x40004,
    fileType=0x1,
    subtype=0x0,
    date=(0, 0)
    ),
  kids=[
    StringFileInfo(
      [
      StringTable(
        u'040904B0',
        [StringStruct(u'CompanyName', u'Halal Crypto Analyzer'),
        StringStruct(u'FileDescription', u'محلل العملات الحلال - Binance Halal Crypto Analyzer'),
        StringStruct(u'FileVersion', u'*******'),
        StringStruct(u'InternalName', u'HalalCryptoAnalyzer'),
        StringStruct(u'LegalCopyright', u'Copyright © 2025'),
        StringStruct(u'OriginalFilename', u'HalalCryptoAnalyzer.exe'),
        StringStruct(u'ProductName', u'Halal Crypto Analyzer'),
        StringStruct(u'ProductVersion', u'*******')])
      ]), 
    VarFileInfo([VarStruct(u'Translation', [1033, 1200])])
  ]
)
'''
    
    with open('version_info.txt', 'w', encoding='utf-8') as f:
        f.write(version_info)
    
    print("✅ تم إنشاء ملف معلومات الإصدار")

def create_icon():
    """إنشاء أيقونة بسيطة للتطبيق"""
    try:
        from PIL import Image, ImageDraw
        
        # إنشاء أيقونة بسيطة
        size = (64, 64)
        img = Image.new('RGBA', size, (0, 128, 255, 255))  # لون أزرق
        draw = ImageDraw.Draw(img)
        
        # رسم دائرة
        draw.ellipse([10, 10, 54, 54], fill=(255, 255, 255, 255))
        
        # رسم رمز العملة
        draw.text((20, 20), "₿", fill=(0, 128, 255, 255))
        
        # حفظ كـ ICO
        img.save('icon.ico', format='ICO')
        print("✅ تم إنشاء أيقونة التطبيق")
        return True
        
    except ImportError:
        print("⚠️ PIL غير متاح، سيتم تخطي إنشاء الأيقونة")
        return False
    except Exception as e:
        print(f"⚠️ خطأ في إنشاء الأيقونة: {e}")
        return False

def build_exe():
    """بناء ملف EXE"""
    print("🔨 بدء بناء ملف EXE...")
    
    try:
        # تشغيل PyInstaller
        cmd = [
            sys.executable, "-m", "PyInstaller",
            "--clean",
            "--noconfirm", 
            "HalalCryptoAnalyzer.spec"
        ]
        
        print("تشغيل PyInstaller...")
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ تم بناء ملف EXE بنجاح!")
            
            # التحقق من وجود الملف
            exe_path = Path("dist/HalalCryptoAnalyzer.exe")
            if exe_path.exists():
                size_mb = exe_path.stat().st_size / (1024 * 1024)
                print(f"📁 مسار الملف: {exe_path.absolute()}")
                print(f"📏 حجم الملف: {size_mb:.1f} MB")
                return True
            else:
                print("❌ لم يتم العثور على ملف EXE")
                return False
        else:
            print("❌ فشل في بناء ملف EXE")
            print("خطأ:", result.stderr)
            return False
            
    except Exception as e:
        print(f"❌ خطأ في بناء ملف EXE: {e}")
        return False

def cleanup():
    """تنظيف الملفات المؤقتة"""
    print("🧹 تنظيف الملفات المؤقتة...")
    
    # حذف مجلدات البناء
    folders_to_remove = ['build', '__pycache__']
    
    for folder in folders_to_remove:
        if os.path.exists(folder):
            try:
                shutil.rmtree(folder)
                print(f"🗑️ تم حذف مجلد: {folder}")
            except Exception as e:
                print(f"⚠️ لم يتم حذف {folder}: {e}")
    
    # حذف ملفات مؤقتة
    temp_files = ['HalalCryptoAnalyzer.spec', 'version_info.txt']
    
    for file in temp_files:
        if os.path.exists(file):
            try:
                os.remove(file)
                print(f"🗑️ تم حذف ملف: {file}")
            except Exception as e:
                print(f"⚠️ لم يتم حذف {file}: {e}")

def create_installer_script():
    """إنشاء سكريبت تثبيت"""
    installer_content = '''@echo off
chcp 65001 >nul
title تثبيت محلل العملات الحلال

echo.
echo ========================================
echo تثبيت محلل العملات الحلال
echo Halal Crypto Analyzer Installer
echo ========================================
echo.

REM إنشاء مجلد التثبيت
set "INSTALL_DIR=%USERPROFILE%\\HalalCryptoAnalyzer"

if not exist "%INSTALL_DIR%" (
    mkdir "%INSTALL_DIR%"
    echo ✅ تم إنشاء مجلد التثبيت: %INSTALL_DIR%
)

REM نسخ الملف التنفيذي
if exist "HalalCryptoAnalyzer.exe" (
    copy "HalalCryptoAnalyzer.exe" "%INSTALL_DIR%\\"
    echo ✅ تم نسخ التطبيق إلى مجلد التثبيت
) else (
    echo ❌ لم يتم العثور على ملف HalalCryptoAnalyzer.exe
    pause
    exit /b 1
)

REM إنشاء اختصار على سطح المكتب
set "DESKTOP=%USERPROFILE%\\Desktop"
set "SHORTCUT=%DESKTOP%\\محلل العملات الحلال.lnk"

powershell -Command "$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%SHORTCUT%'); $Shortcut.TargetPath = '%INSTALL_DIR%\\HalalCryptoAnalyzer.exe'; $Shortcut.WorkingDirectory = '%INSTALL_DIR%'; $Shortcut.Description = 'محلل العملات الحلال'; $Shortcut.Save()"

if exist "%SHORTCUT%" (
    echo ✅ تم إنشاء اختصار على سطح المكتب
) else (
    echo ⚠️ لم يتم إنشاء اختصار على سطح المكتب
)

echo.
echo ✅ تم التثبيت بنجاح!
echo 📁 مجلد التثبيت: %INSTALL_DIR%
echo 🖥️ اختصار سطح المكتب: %SHORTCUT%
echo.
echo يمكنك الآن تشغيل التطبيق من سطح المكتب أو من مجلد التثبيت
echo.

pause
'''
    
    with open('install.bat', 'w', encoding='utf-8') as f:
        f.write(installer_content)
    
    print("✅ تم إنشاء سكريبت التثبيت: install.bat")

def main():
    """الدالة الرئيسية"""
    print("🚀 بناء ملف EXE لمحلل العملات الحلال")
    print("=" * 50)
    
    # التحقق من PyInstaller
    if not check_pyinstaller():
        return False
    
    # إنشاء الملفات المطلوبة
    create_spec_file()
    create_version_info()
    create_icon()
    
    # بناء ملف EXE
    if build_exe():
        print("\n🎉 تم بناء ملف EXE بنجاح!")
        
        # إنشاء سكريبت التثبيت
        create_installer_script()
        
        print("\n📋 الملفات المُنشأة:")
        print("- dist/HalalCryptoAnalyzer.exe (الملف التنفيذي)")
        print("- install.bat (سكريبت التثبيت)")
        
        print("\n🎯 كيفية الاستخدام:")
        print("1. شغل install.bat لتثبيت التطبيق")
        print("2. أو شغل HalalCryptoAnalyzer.exe مباشرة من مجلد dist")
        
        # تنظيف الملفات المؤقتة
        cleanup_choice = input("\nهل تريد تنظيف الملفات المؤقتة؟ (y/n): ")
        if cleanup_choice.lower() in ['y', 'yes', 'نعم']:
            cleanup()
        
        return True
    else:
        print("\n❌ فشل في بناء ملف EXE")
        return False

if __name__ == "__main__":
    success = main()
    if not success:
        input("اضغط Enter للخروج...")
        sys.exit(1)
    else:
        input("اضغط Enter للخروج...")
