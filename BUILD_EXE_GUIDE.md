# دليل بناء ملف EXE - محلل العملات الحلال

## 🎯 نظرة عامة

هذا الدليل يوضح كيفية إنشاء ملف تنفيذي (.exe) مستقل للتطبيق باستخدام PyInstaller، بحيث يمكن تشغيله على أي جهاز ويندوز بدون الحاجة لتثبيت Python أو أي مكتبات.

## 📋 المتطلبات

### متطلبات النظام:
- **Windows 10/11** (للبناء على ويندوز)
- **Python 3.8+** مثبت ومضاف إلى PATH
- **8GB RAM** كحد أدنى (16GB مُوصى به)
- **2GB مساحة فارغة** للبناء والملفات المؤقتة

### المكتبات المطلوبة:
```bash
pip install pyinstaller pillow
```

## 🚀 طرق البناء

### الطريقة الأولى: التلقائية (مُوصى بها)

#### للويندوز:
```bash
# انقر مزدوجاً على الملف أو شغل من Command Prompt
build_exe.bat
```

#### للينكس/ماك:
```bash
# اجعل الملف قابل للتنفيذ
chmod +x build_exe.sh

# شغل الملف
./build_exe.sh
```

### الطريقة الثانية: اليدوية

```bash
# 1. تثبيت المتطلبات
pip install -r requirements.txt

# 2. تشغيل سكريبت البناء
python build_exe.py

# 3. أو استخدام PyInstaller مباشرة
pyinstaller --onefile --windowed --name="HalalCryptoAnalyzer" main.py
```

## 📁 الملفات المُنشأة

بعد البناء الناجح، ستجد:

```
📦 المشروع
├── 📁 dist/
│   └── 🚀 HalalCryptoAnalyzer.exe    # الملف التنفيذي الرئيسي
├── 📄 install.bat                    # سكريبت التثبيت
├── 🖼️ icon.ico                      # أيقونة التطبيق
└── 📋 version_info.txt               # معلومات الإصدار
```

### تفاصيل الملفات:

- **`HalalCryptoAnalyzer.exe`**: الملف التنفيذي الرئيسي (~150-200 MB)
- **`install.bat`**: سكريبت تثبيت التطبيق في النظام
- **`icon.ico`**: أيقونة التطبيق المخصصة
- **`version_info.txt`**: معلومات الإصدار والشركة

## 🔧 خيارات التخصيص

### تخصيص الأيقونة:
```python
# في build_exe.py، يمكن تعديل دالة create_icon()
# أو استبدال icon.ico بأيقونة مخصصة
```

### تخصيص معلومات الإصدار:
```python
# تعديل version_info.txt لتغيير:
# - اسم الشركة
# - وصف الملف
# - رقم الإصدار
# - حقوق النشر
```

### تخصيص الملفات المضمنة:
```python
# في ملف .spec، يمكن إضافة ملفات إضافية:
added_files = [
    ('config.py', '.'),
    ('my_custom_file.txt', '.'),
]
```

## 🎛️ خيارات PyInstaller المتقدمة

### بناء ملف واحد (One-file):
```bash
pyinstaller --onefile main.py
```
- **المميزات**: ملف واحد سهل التوزيع
- **العيوب**: بطء في البدء، حجم أكبر

### بناء مجلد (One-folder):
```bash
pyinstaller --onedir main.py
```
- **المميزات**: بدء أسرع، حجم أصغر
- **العيوب**: عدة ملفات للتوزيع

### إخفاء نافذة الكونسول:
```bash
pyinstaller --windowed main.py
```

### تحسين الحجم:
```bash
pyinstaller --onefile --windowed --strip --upx-dir=/path/to/upx main.py
```

## 📊 أحجام الملفات المتوقعة

| نوع البناء | الحجم التقريبي | وقت البدء |
|-------------|----------------|-----------|
| One-file | 180-220 MB | 10-15 ثانية |
| One-folder | 150-180 MB | 3-5 ثواني |
| مضغوط بـ UPX | 120-150 MB | 5-8 ثواني |

## 🛠️ حل المشاكل الشائعة

### مشكلة: "Failed to execute script"
```bash
# الحل: إضافة المكتبات المفقودة إلى hiddenimports
hiddenimports=['missing_module']
```

### مشكلة: "No module named 'PyQt6'"
```bash
# الحل: تثبيت PyQt6 في البيئة الحالية
pip install PyQt6
```

### مشكلة: حجم الملف كبير جداً
```bash
# الحل: استبعاد المكتبات غير المطلوبة
excludes=['tkinter', 'test', 'unittest']
```

### مشكلة: بطء في البدء
```bash
# الحل: استخدام one-folder بدلاً من one-file
pyinstaller --onedir main.py
```

### مشكلة: خطأ في الأيقونة
```bash
# الحل: التأكد من صيغة ICO صحيحة
# أو حذف icon.ico واستخدام الأيقونة الافتراضية
```

## 🔍 اختبار الملف التنفيذي

### اختبار أساسي:
```bash
# 1. تشغيل الملف مباشرة
dist/HalalCryptoAnalyzer.exe

# 2. اختبار على جهاز آخر بدون Python
# نسخ الملف إلى جهاز نظيف وتشغيله
```

### اختبار متقدم:
```bash
# 1. اختبار جميع الوظائف
# 2. اختبار الاتصال بـ Binance API
# 3. اختبار التصدير والحفظ
# 4. اختبار الإشعارات
```

## 📦 التوزيع

### للمستخدمين العاديين:
1. نسخ `HalalCryptoAnalyzer.exe` و `install.bat`
2. تشغيل `install.bat` لتثبيت التطبيق
3. استخدام الاختصار من سطح المكتب

### للمطورين:
1. توزيع مجلد `dist` كاملاً
2. إرفاق دليل الاستخدام
3. تضمين ملف README

## 🔄 التحديثات

### تحديث الإصدار:
1. تعديل رقم الإصدار في `version_info.txt`
2. إعادة بناء الملف التنفيذي
3. اختبار الإصدار الجديد
4. توزيع التحديث

### تحديث المكتبات:
```bash
# 1. تحديث requirements.txt
# 2. إعادة تثبيت المتطلبات
pip install -r requirements.txt --upgrade

# 3. إعادة بناء الملف التنفيذي
python build_exe.py
```

## 📋 قائمة التحقق

قبل التوزيع، تأكد من:

- [ ] الملف التنفيذي يعمل على جهاز نظيف
- [ ] جميع الوظائف تعمل بشكل صحيح
- [ ] الأيقونة تظهر بشكل صحيح
- [ ] معلومات الإصدار صحيحة
- [ ] حجم الملف معقول
- [ ] لا توجد أخطاء في وقت التشغيل
- [ ] الإشعارات تعمل
- [ ] التصدير يعمل
- [ ] قاعدة البيانات تُنشأ بشكل صحيح

## 🆘 الدعم

إذا واجهت مشاكل في البناء:

1. **تحقق من السجلات**: راجع رسائل الخطأ في الكونسول
2. **اختبر البيئة**: تأكد من عمل التطبيق بـ Python أولاً
3. **تحديث المكتبات**: استخدم أحدث إصدارات PyInstaller
4. **راجع الوثائق**: [PyInstaller Documentation](https://pyinstaller.readthedocs.io/)

---

**ملاحظة**: بناء ملف EXE قد يستغرق 5-15 دقيقة حسب سرعة الجهاز وحجم المكتبات.
