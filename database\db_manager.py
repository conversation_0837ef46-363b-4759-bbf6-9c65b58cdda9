# -*- coding: utf-8 -*-
"""
مدير قاعدة البيانات SQLite
يتعامل مع حفظ واسترجاع البيانات التاريخية والتحليلات
"""

import sqlite3
import pandas as pd
import json
from datetime import datetime
from typing import Dict, List, Optional
import config
import os

class DatabaseManager:
    """كلاس إدارة قاعدة البيانات"""
    
    def __init__(self, db_path: str = None):
        """
        تهيئة مدير قاعدة البيانات
        
        Args:
            db_path (str): مسار قاعدة البيانات
        """
        self.db_path = db_path or config.DATABASE_PATH
        self.ensure_data_directory()
        self.init_database()
    
    def ensure_data_directory(self):
        """التأكد من وجود مجلد البيانات"""
        data_dir = os.path.dirname(self.db_path)
        if not os.path.exists(data_dir):
            os.makedirs(data_dir)
    
    def get_connection(self) -> sqlite3.Connection:
        """
        الحصول على اتصال بقاعدة البيانات
        
        Returns:
            sqlite3.Connection: اتصال قاعدة البيانات
        """
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row
        return conn
    
    def init_database(self):
        """إنشاء جداول قاعدة البيانات"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            
            # جدول بيانات الأسعار التاريخية
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS price_data (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    symbol TEXT NOT NULL,
                    timestamp DATETIME NOT NULL,
                    open_price REAL NOT NULL,
                    high_price REAL NOT NULL,
                    low_price REAL NOT NULL,
                    close_price REAL NOT NULL,
                    volume REAL NOT NULL,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    UNIQUE(symbol, timestamp)
                )
            ''')
            
            # جدول التحليلات الفنية
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS technical_analysis (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    symbol TEXT NOT NULL,
                    analysis_date DATETIME NOT NULL,
                    rsi REAL,
                    macd REAL,
                    macd_signal REAL,
                    macd_histogram REAL,
                    sma_short REAL,
                    sma_long REAL,
                    trend TEXT,
                    overall_signal TEXT,
                    support_level REAL,
                    resistance_level REAL,
                    analysis_data TEXT,  -- JSON للبيانات الإضافية
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    UNIQUE(symbol, analysis_date)
                )
            ''')
            
            # جدول الإشعارات
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS notifications (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    symbol TEXT NOT NULL,
                    notification_type TEXT NOT NULL,
                    message TEXT NOT NULL,
                    price REAL,
                    is_read BOOLEAN DEFAULT FALSE,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # جدول إعدادات المستخدم
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS user_settings (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    setting_key TEXT UNIQUE NOT NULL,
                    setting_value TEXT NOT NULL,
                    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # إنشاء فهارس للأداء
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_price_symbol_timestamp ON price_data(symbol, timestamp)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_analysis_symbol_date ON technical_analysis(symbol, analysis_date)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_notifications_symbol ON notifications(symbol)')
            
            conn.commit()
    
    def save_price_data(self, symbol: str, data: pd.DataFrame):
        """
        حفظ بيانات الأسعار
        
        Args:
            symbol (str): رمز العملة
            data (pd.DataFrame): بيانات الأسعار
        """
        with self.get_connection() as conn:
            cursor = conn.cursor()
            
            for index, row in data.iterrows():
                cursor.execute('''
                    INSERT OR REPLACE INTO price_data 
                    (symbol, timestamp, open_price, high_price, low_price, close_price, volume)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                ''', (
                    symbol,
                    index.strftime('%Y-%m-%d %H:%M:%S'),
                    float(row['open']),
                    float(row['high']),
                    float(row['low']),
                    float(row['close']),
                    float(row['volume'])
                ))
            
            conn.commit()
    
    def get_price_data(self, symbol: str, start_date: str = None, end_date: str = None) -> pd.DataFrame:
        """
        استرجاع بيانات الأسعار
        
        Args:
            symbol (str): رمز العملة
            start_date (str): تاريخ البداية
            end_date (str): تاريخ النهاية
            
        Returns:
            pd.DataFrame: بيانات الأسعار
        """
        with self.get_connection() as conn:
            query = '''
                SELECT timestamp, open_price as open, high_price as high, 
                       low_price as low, close_price as close, volume
                FROM price_data 
                WHERE symbol = ?
            '''
            params = [symbol]
            
            if start_date:
                query += ' AND timestamp >= ?'
                params.append(start_date)
            
            if end_date:
                query += ' AND timestamp <= ?'
                params.append(end_date)
            
            query += ' ORDER BY timestamp'
            
            df = pd.read_sql_query(query, conn, params=params)
            
            if not df.empty:
                df['timestamp'] = pd.to_datetime(df['timestamp'])
                df.set_index('timestamp', inplace=True)
            
            return df
    
    def save_analysis(self, symbol: str, analysis: Dict):
        """
        حفظ التحليل الفني
        
        Args:
            symbol (str): رمز العملة
            analysis (Dict): بيانات التحليل
        """
        with self.get_connection() as conn:
            cursor = conn.cursor()
            
            analysis_date = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            
            cursor.execute('''
                INSERT OR REPLACE INTO technical_analysis 
                (symbol, analysis_date, rsi, macd, macd_signal, macd_histogram,
                 sma_short, sma_long, trend, overall_signal, support_level, 
                 resistance_level, analysis_data)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                symbol,
                analysis_date,
                analysis.get('rsi'),
                analysis.get('macd', {}).get('macd'),
                analysis.get('macd', {}).get('signal'),
                analysis.get('macd', {}).get('histogram'),
                analysis.get('moving_averages', {}).get('sma_short'),
                analysis.get('moving_averages', {}).get('sma_long'),
                analysis.get('trend'),
                analysis.get('signals', {}).get('overall_signal'),
                analysis.get('support_resistance', {}).get('support'),
                analysis.get('support_resistance', {}).get('resistance'),
                json.dumps(analysis, ensure_ascii=False)
            ))
            
            conn.commit()
    
    def get_latest_analysis(self, symbol: str) -> Dict:
        """
        الحصول على آخر تحليل للعملة
        
        Args:
            symbol (str): رمز العملة
            
        Returns:
            Dict: بيانات التحليل
        """
        with self.get_connection() as conn:
            cursor = conn.cursor()
            
            cursor.execute('''
                SELECT * FROM technical_analysis 
                WHERE symbol = ? 
                ORDER BY analysis_date DESC 
                LIMIT 1
            ''', (symbol,))
            
            row = cursor.fetchone()
            
            if row:
                analysis = dict(row)
                if analysis['analysis_data']:
                    try:
                        analysis['full_data'] = json.loads(analysis['analysis_data'])
                    except:
                        analysis['full_data'] = {}
                return analysis
            
            return {}
    
    def save_notification(self, symbol: str, notification_type: str, message: str, price: float = None):
        """
        حفظ إشعار
        
        Args:
            symbol (str): رمز العملة
            notification_type (str): نوع الإشعار
            message (str): رسالة الإشعار
            price (float): السعر (اختياري)
        """
        with self.get_connection() as conn:
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT INTO notifications (symbol, notification_type, message, price)
                VALUES (?, ?, ?, ?)
            ''', (symbol, notification_type, message, price))
            
            conn.commit()
    
    def get_unread_notifications(self) -> List[Dict]:
        """
        الحصول على الإشعارات غير المقروءة
        
        Returns:
            List[Dict]: قائمة الإشعارات
        """
        with self.get_connection() as conn:
            cursor = conn.cursor()
            
            cursor.execute('''
                SELECT * FROM notifications 
                WHERE is_read = FALSE 
                ORDER BY created_at DESC
            ''')
            
            return [dict(row) for row in cursor.fetchall()]
    
    def mark_notification_read(self, notification_id: int):
        """
        تحديد الإشعار كمقروء
        
        Args:
            notification_id (int): معرف الإشعار
        """
        with self.get_connection() as conn:
            cursor = conn.cursor()
            
            cursor.execute('''
                UPDATE notifications 
                SET is_read = TRUE 
                WHERE id = ?
            ''', (notification_id,))
            
            conn.commit()
    
    def get_setting(self, key: str, default_value: str = None) -> str:
        """
        الحصول على إعداد
        
        Args:
            key (str): مفتاح الإعداد
            default_value (str): القيمة الافتراضية
            
        Returns:
            str: قيمة الإعداد
        """
        with self.get_connection() as conn:
            cursor = conn.cursor()
            
            cursor.execute('SELECT setting_value FROM user_settings WHERE setting_key = ?', (key,))
            row = cursor.fetchone()
            
            return row['setting_value'] if row else default_value
    
    def save_setting(self, key: str, value: str):
        """
        حفظ إعداد
        
        Args:
            key (str): مفتاح الإعداد
            value (str): قيمة الإعداد
        """
        with self.get_connection() as conn:
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT OR REPLACE INTO user_settings (setting_key, setting_value, updated_at)
                VALUES (?, ?, CURRENT_TIMESTAMP)
            ''', (key, value))
            
            conn.commit()
