# -*- coding: utf-8 -*-
"""
ملف الإعدادات الرئيسي للتطبيق
"""

import os

# إعدادات Binance API
BINANCE_API_KEY = os.getenv('BINANCE_API_KEY', '')
BINANCE_API_SECRET = os.getenv('BINANCE_API_SECRET', '')

# إعدادات قاعدة البيانات
DATABASE_PATH = 'data/crypto_analyzer.db'

# إعدادات التحديث
UPDATE_INTERVAL = 60  # ثانية
CHART_TIMEFRAME = '1h'  # إطار زمني للرسوم البيانية

# إعدادات المؤشرات الفنية
RSI_PERIOD = 14
MACD_FAST = 12
MACD_SLOW = 26
MACD_SIGNAL = 9
MA_SHORT = 20
MA_LONG = 50

# حدود المؤشرات
RSI_OVERSOLD = 30
RSI_OVERBOUGHT = 70

# إعدادات الواجهة
WINDOW_WIDTH = 1200
WINDOW_HEIGHT = 800
LANGUAGE = 'ar'  # ar للعربية، en للإنجليزية

# مسارات الملفات
DATA_DIR = 'data'
LOGS_DIR = 'logs'
CHARTS_DIR = 'charts'

# إنشاء المجلدات إذا لم تكن موجودة
for directory in [DATA_DIR, LOGS_DIR, CHARTS_DIR]:
    if not os.path.exists(directory):
        os.makedirs(directory)
