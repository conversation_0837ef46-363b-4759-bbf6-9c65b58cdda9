# دليل حل مشاكل PyQt - محلل العملات الحلال

## 🚨 المشكلة الشائعة

```
ImportError: DLL load failed while importing QtCore: The specified procedure could not be found.
```

هذا خطأ شائع مع PyQt6 على ويندوز. إليك الحلول المرتبة حسب الفعالية:

## 🛠️ الحلول السريعة

### الحل 1: استخدام سكريبت الإصلاح التلقائي ⭐
```bash
python fix_pyqt.py
```
هذا السكريبت سيحاول جميع الحلول تلقائياً.

### الحل 2: التثبيت الذكي ⭐
```bash
python install_smart.py
```
يختبر ويثبت أفضل مكتبة Qt متاحة.

### الحل 3: استخدام PyQt5 بدلاً من PyQt6 ⭐⭐⭐
```bash
# إلغاء تثبيت PyQt6
pip uninstall PyQt6 -y

# تثبيت PyQt5 (أكثر استقراراً)
pip install PyQt5==5.15.10

# تشغيل التطبيق
python main_pyqt5.py
```

## 🔧 الحلول المتقدمة

### الحل 4: تثبيت Visual C++ Redistributable
1. **تحميل وتثبيت**:
   - اذهب إلى: https://aka.ms/vs/17/release/vc_redist.x64.exe
   - حمل وثبت الملف
   - أعد تشغيل الجهاز

2. **اختبار**:
   ```bash
   python -c "from PyQt6.QtWidgets import QApplication; print('✅ PyQt6 يعمل')"
   ```

### الحل 5: استخدام Anaconda
```bash
# تثبيت Anaconda من anaconda.com
# ثم في Anaconda Prompt:
conda install pyqt
conda install pandas matplotlib numpy requests

# تشغيل التطبيق
python main.py
```

### الحل 6: استخدام PySide6
```bash
# إلغاء تثبيت PyQt
pip uninstall PyQt6 PyQt5 -y

# تثبيت PySide6 (البديل الرسمي)
pip install PySide6

# تشغيل التطبيق
python main_pyqt5.py  # يدعم PySide6 تلقائياً
```

## 🧪 اختبار الحلول

### اختبار سريع:
```bash
python -c "
try:
    from PyQt6.QtWidgets import QApplication
    print('✅ PyQt6 يعمل')
except:
    try:
        from PyQt5.QtWidgets import QApplication
        print('✅ PyQt5 يعمل')
    except:
        try:
            from PySide6.QtWidgets import QApplication
            print('✅ PySide6 يعمل')
        except:
            print('❌ لا توجد مكتبة Qt تعمل')
"
```

### اختبار شامل:
```bash
python test_app.py
```

## 📋 ملفات المتطلبات المختلفة

### للـ PyQt5 (الأكثر استقراراً):
```bash
pip install -r requirements_pyqt5.txt
```

### للـ PyQt6 (الأحدث):
```bash
pip install -r requirements_pyqt6.txt
```

### للـ PySide6 (البديل الرسمي):
```bash
pip install -r requirements_pyside6.txt
```

## 🔍 تشخيص المشاكل

### فحص إصدار Python:
```bash
python --version
# يجب أن يكون 3.8 أو أحدث
```

### فحص المكتبات المثبتة:
```bash
pip list | findstr -i qt
```

### فحص متغيرات البيئة:
```bash
echo %PATH%
# تأكد من وجود Python في PATH
```

## 🚀 ملفات التشغيل المحدثة

### للتشغيل التلقائي مع حل المشاكل:
```bash
run.bat  # ويندوز
./run.sh # لينكس/ماك
```

### للتشغيل اليدوي:
```bash
# جرب بالترتيب:
python main_pyqt5.py    # يدعم جميع إصدارات Qt
python main.py          # الإصدار الأصلي
```

## 🆘 إذا فشلت جميع الحلول

### الحل الأخير - إعادة تثبيت Python:
1. **إلغاء تثبيت Python** من Control Panel
2. **تحميل Python الجديد** من python.org
3. **تثبيت مع تفعيل "Add to PATH"**
4. **إعادة تثبيت المتطلبات**:
   ```bash
   python install_smart.py
   ```

### استخدام بيئة افتراضية جديدة:
```bash
# إنشاء بيئة جديدة
python -m venv fresh_env

# تفعيل البيئة
fresh_env\Scripts\activate  # ويندوز
source fresh_env/bin/activate  # لينكس/ماك

# تثبيت المتطلبات
pip install -r requirements_pyqt5.txt

# تشغيل التطبيق
python main_pyqt5.py
```

## 📊 مقارنة مكتبات Qt

| المكتبة | الاستقرار | الحداثة | سهولة التثبيت | التوصية |
|---------|-----------|---------|----------------|----------|
| **PyQt5** | ⭐⭐⭐⭐⭐ | ⭐⭐⭐ | ⭐⭐⭐⭐⭐ | **الأفضل للمبتدئين** |
| **PyQt6** | ⭐⭐⭐ | ⭐⭐⭐⭐⭐ | ⭐⭐ | للمطورين المتقدمين |
| **PySide6** | ⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ | ⭐⭐⭐ | البديل الرسمي |

## 🎯 التوصية النهائية

**للمستخدمين العاديين**: استخدم PyQt5
```bash
pip install PyQt5==5.15.10
python main_pyqt5.py
```

**للمطورين**: جرب الترتيب التالي:
1. PyQt5 (الأكثر استقراراً)
2. PySide6 (البديل الرسمي)
3. PyQt6 (الأحدث، لكن قد يحتاج إعداد إضافي)

## 📞 الدعم

إذا استمرت المشاكل:
1. **شغل التشخيص**: `python fix_pyqt.py`
2. **راجع السجلات**: مجلد `logs/`
3. **اختبر البيئة**: `python test_app.py`
4. **استخدم بيئة افتراضية جديدة**

---

**ملاحظة**: معظم مشاكل PyQt6 تحل بتثبيت Visual C++ Redistributable أو التبديل إلى PyQt5.
