@echo off
chcp 65001 >nul
title إصلاح سريع - محلل العملات الحلال

echo.
echo ========================================
echo إصلاح سريع لمشاكل PyQt
echo Quick Fix for PyQt Issues
echo ========================================
echo.

echo 🔧 تشخيص المشكلة...

REM التحقق من Python
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python غير مثبت أو غير موجود في PATH
    echo يرجى تثبيت Python من python.org
    pause
    exit /b 1
)

echo ✅ Python متاح

REM اختبار PyQt
echo.
echo 🧪 اختبار مكتبات Qt...

python -c "from PyQt6.QtWidgets import QApplication; print('✅ PyQt6 يعمل')" 2>nul
if not errorlevel 1 (
    echo PyQt6 يعمل بشكل صحيح
    goto :run_app
)

python -c "from PyQt5.QtWidgets import QApplication; print('✅ PyQt5 يعمل')" 2>nul
if not errorlevel 1 (
    echo PyQt5 يعمل بشكل صحيح
    goto :run_app
)

python -c "from PySide6.QtWidgets import QApplication; print('✅ PySide6 يعمل')" 2>nul
if not errorlevel 1 (
    echo PySide6 يعمل بشكل صحيح
    goto :run_app
)

echo ❌ لا توجد مكتبة Qt تعمل
echo.

echo 🛠️ تطبيق الإصلاحات...

REM الحل 1: إلغاء تثبيت PyQt6 وتثبيت PyQt5
echo 📦 الحل 1: التبديل إلى PyQt5...
python -m pip uninstall PyQt6 -y >nul 2>&1
python -m pip install PyQt5==5.15.10

REM اختبار PyQt5
python -c "from PyQt5.QtWidgets import QApplication; print('✅ PyQt5 يعمل الآن')" 2>nul
if not errorlevel 1 (
    echo ✅ تم حل المشكلة باستخدام PyQt5
    goto :run_app
)

REM الحل 2: تثبيت PySide6
echo 📦 الحل 2: تثبيت PySide6...
python -m pip install PySide6

REM اختبار PySide6
python -c "from PySide6.QtWidgets import QApplication; print('✅ PySide6 يعمل الآن')" 2>nul
if not errorlevel 1 (
    echo ✅ تم حل المشكلة باستخدام PySide6
    goto :run_app
)

REM إذا فشلت جميع الحلول
echo.
echo ❌ فشلت الحلول التلقائية
echo.
echo 🆘 حلول يدوية:
echo 1. تثبيت Visual C++ Redistributable:
echo    https://aka.ms/vs/17/release/vc_redist.x64.exe
echo.
echo 2. أعد تشغيل الجهاز وجرب مرة أخرى
echo.
echo 3. استخدم Anaconda:
echo    conda install pyqt
echo.
echo 4. شغل الإصلاح المتقدم:
echo    python fix_pyqt.py
echo.
pause
exit /b 1

:run_app
echo.
echo 🚀 تشغيل التطبيق...
python main_pyqt5.py

if errorlevel 1 (
    echo.
    echo ⚠️ فشل في التشغيل، محاولة الإصدار الأصلي...
    python main.py
    
    if errorlevel 1 (
        echo.
        echo ❌ فشل في تشغيل التطبيق
        echo راجع PYQT_TROUBLESHOOTING.md للمزيد من الحلول
        pause
    )
)

echo.
echo تم إغلاق التطبيق
pause
