# دليل التشغيل السريع - محلل العملات الحلال

## 🚀 التشغيل السريع

### للويندوز:
```bash
# انقر مزدوجاً على الملف أو شغل من Command Prompt
run.bat
```

### للينكس/ماك:
```bash
# اجعل الملف قابل للتنفيذ
chmod +x run.sh

# شغل الملف
./run.sh
```

### التشغيل اليدوي:
```bash
# تثبيت المتطلبات
pip install -r requirements.txt

# تشغيل التطبيق
python main.py
```

## 📋 متطلبات سريعة

- **Python 3.8+** 
- **اتصال إنترنت** (لجلب البيانات من Binance)
- **4GB RAM** كحد أدنى

## 🔧 إعداد سريع (اختياري)

1. انسخ `.env.example` إلى `.env`
2. أضف مفاتيح Binance API (اختياري للبيانات العامة)

## 🎯 الاستخدام السريع

1. **تشغيل التطبيق**: استخدم أحد ملفات التشغيل أعلاه
2. **عرض العملات**: ستظهر قائمة العملات الحلال تلقائياً
3. **التحديث**: انقر "تحديث الكل" لجلب أحدث البيانات
4. **التحليل المفصل**: انقر مزدوجاً على أي عملة
5. **الفلترة**: استخدم "إشارات الشراء فقط" لعرض الفرص
6. **التصدير**: من قائمة "ملف" > "تصدير البيانات"

## 🔔 الإشعارات

- ستظهر أيقونة في شريط النظام
- إشعارات تلقائية عند ظهور إشارات جديدة
- يمكن تخصيص الإعدادات من الواجهة

## 📊 المؤشرات المدعومة

- **RSI**: مؤشر القوة النسبية
- **MACD**: تقارب وتباعد المتوسطات المتحركة  
- **MA**: المتوسطات المتحركة
- **نطاقات بولينجر**: لقياس التقلبات
- **مستويات الدعم والمقاومة**

## 🛠️ حل المشاكل السريع

### مشكلة: "DLL load failed while importing QtCore" ⚠️ الأكثر شيوعاً
```bash
# الحل السريع (مُوصى به):
quick_fix.bat

# أو الحل اليدوي:
pip uninstall PyQt6 -y
pip install PyQt5==5.15.10
python main_pyqt5.py
```

### مشكلة: "Python غير موجود"
```bash
# تثبيت Python من python.org
# تأكد من إضافته إلى PATH
```

### مشكلة: "فشل تثبيت المتطلبات"
```bash
# الحل الذكي:
python install_smart.py

# أو الحل اليدوي:
python -m pip install --upgrade pip
pip install -r requirements_pyqt5.txt
```

### مشكلة: "خطأ في الاتصال بـ Binance"
- تحقق من اتصال الإنترنت
- التطبيق يعمل بدون مفاتيح API للبيانات العامة

### مشكلة: "خطأ في الواجهة الرسومية"
```bash
# جرب بالترتيب:
python fix_pyqt.py          # إصلاح تلقائي
pip install PyQt5           # الأكثر استقراراً
pip install PySide6         # البديل الرسمي
```

### مشكلة: "التطبيق لا يبدأ"
```bash
# التشخيص:
python test_app.py

# الحلول:
python main_pyqt5.py        # يدعم جميع إصدارات Qt
python main.py              # الإصدار الأصلي
```

## 📁 هيكل الملفات

```
📦 binance-halal-crypto-analyzer
├── 🚀 main.py              # نقطة البداية
├── 🔧 config.py           # الإعدادات
├── 📋 requirements.txt    # المتطلبات
├── 🏃 run.bat             # تشغيل ويندوز
├── 🏃 run.sh              # تشغيل لينكس/ماك
├── 🧪 test_app.py         # اختبار التطبيق
├── 📚 README.md           # الدليل الكامل
├── 📖 QUICK_START.md      # هذا الملف
├── 📁 api/                # وحدات API
├── 📁 analysis/           # التحليل الفني
├── 📁 database/           # قاعدة البيانات
├── 📁 gui/                # الواجهة الرسومية
├── 📁 utils/              # أدوات مساعدة
├── 📁 data/               # بيانات التطبيق
├── 📁 logs/               # ملفات السجل
├── 📁 charts/             # الرسوم البيانية
└── 📁 exports/            # الملفات المُصدَّرة
```

## ⚠️ تنويه مهم

هذا التطبيق للأغراض التعليمية فقط. لا يُعتبر نصيحة استثمارية.
استشر مستشار مالي قبل اتخاذ أي قرارات استثمارية.

## 🆘 الدعم

- راجع `README.md` للدليل الكامل
- شغل `test_app.py` لاختبار التطبيق
- تحقق من مجلد `logs/` للأخطاء

---
**تم بواسطة AI Assistant - 2025** 🤖
