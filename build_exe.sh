#!/bin/bash

# بناء ملف تنفيذي للينكس/ماك - محلل العملات الحلال

echo "========================================"
echo "بناء ملف تنفيذي - محلل العملات الحلال"
echo "Building Executable - Halal Crypto Analyzer"
echo "========================================"
echo

# التحقق من وجود Python
if ! command -v python3 &> /dev/null; then
    if ! command -v python &> /dev/null; then
        echo "❌ خطأ: Python غير مثبت"
        echo "يرجى تثبيت Python 3.8 أو أحدث"
        exit 1
    else
        PYTHON_CMD="python"
    fi
else
    PYTHON_CMD="python3"
fi

echo "✅ تم العثور على Python"

# التحقق من إصدار Python
PYTHON_VERSION=$($PYTHON_CMD -c "import sys; print(f'{sys.version_info.major}.{sys.version_info.minor}')")
REQUIRED_VERSION="3.8"

if [ "$(printf '%s\n' "$REQUIRED_VERSION" "$PYTHON_VERSION" | sort -V | head -n1)" != "$REQUIRED_VERSION" ]; then
    echo "❌ خطأ: يتطلب Python 3.8 أو أحدث"
    echo "الإصدار الحالي: $PYTHON_VERSION"
    exit 1
fi

echo "✅ إصدار Python مناسب: $PYTHON_VERSION"
echo

# التحقق من المتطلبات وتثبيتها
echo "🔄 التحقق من المتطلبات..."

# تثبيت PyInstaller إذا لم يكن مثبتاً
$PYTHON_CMD -c "import PyInstaller" 2>/dev/null
if [ $? -ne 0 ]; then
    echo "📦 تثبيت PyInstaller..."
    $PYTHON_CMD -m pip install pyinstaller
fi

# تثبيت Pillow إذا لم يكن مثبتاً
$PYTHON_CMD -c "import PIL" 2>/dev/null
if [ $? -ne 0 ]; then
    echo "📦 تثبيت Pillow..."
    $PYTHON_CMD -m pip install pillow
fi

echo "✅ المتطلبات جاهزة"
echo

# تشغيل سكريبت البناء
echo "🔨 بدء بناء الملف التنفيذي..."
$PYTHON_CMD build_exe.py

if [ $? -ne 0 ]; then
    echo
    echo "❌ فشل في بناء الملف التنفيذي"
    exit 1
fi

echo
echo "✅ تم بناء الملف التنفيذي بنجاح!"
echo

# التحقق من وجود الملف
if [ -f "dist/HalalCryptoAnalyzer" ]; then
    echo "📁 الملف موجود في: dist/HalalCryptoAnalyzer"
    
    # حساب حجم الملف
    size=$(du -m "dist/HalalCryptoAnalyzer" | cut -f1)
    echo "📏 حجم الملف: ${size} MB تقريباً"
    
    # جعل الملف قابل للتنفيذ
    chmod +x "dist/HalalCryptoAnalyzer"
    echo "✅ تم جعل الملف قابل للتنفيذ"
    
    echo
    echo "🎯 الخطوات التالية:"
    echo "1. شغل الملف: ./dist/HalalCryptoAnalyzer"
    echo "2. يمكن نسخ الملف إلى أي نظام مشابه بدون تثبيت Python"
    echo "3. تأكد من وجود المكتبات النظام المطلوبة (Qt6, etc.)"
    
else
    echo "❌ لم يتم العثور على الملف التنفيذي"
fi

echo
read -p "اضغط Enter للمتابعة..."
