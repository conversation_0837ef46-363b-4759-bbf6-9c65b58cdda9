# تعليمات الملف التنفيذي - محلل العملات الحلال

## 🎯 للمستخدمين الجدد

### ✅ ما تحتاجه:
- **جهاز ويندوز 10/11**
- **4GB RAM** كحد أدنى
- **اتصال إنترنت** للبيانات المباشرة
- **لا تحتاج تثبيت Python!** 🎉

### 🚀 التشغيل السريع:

#### الطريقة الأولى: التثبيت (مُوصى بها)
1. **تحميل الملفات**: احصل على `HalalCryptoAnalyzer.exe` و `install.bat`
2. **التثبيت**: انقر مزدوجاً على `install.bat`
3. **التشغيل**: استخدم الاختصار من سطح المكتب

#### الطريقة الثانية: التشغيل المباشر
1. **تحميل الملف**: احصل على `HalalCryptoAnalyzer.exe`
2. **التشغيل**: انقر مزدوجاً على الملف

## 📋 ما يحدث عند التثبيت:

```
📁 سيتم إنشاء مجلد في:
   C:\Users\<USER>\HalalCryptoAnalyzer\

🖥️ سيتم إنشاء اختصار على سطح المكتب:
   "محلل العملات الحلال"

📊 سيتم إنشاء مجلدات البيانات تلقائياً:
   - data/     (قاعدة البيانات)
   - logs/     (ملفات السجل)
   - exports/  (الملفات المُصدَّرة)
```

## 🎮 كيفية الاستخدام:

### عند التشغيل الأول:
1. **انتظر التحميل**: قد يستغرق 10-15 ثانية في المرة الأولى
2. **السماح للجدار الناري**: اختر "السماح" إذا ظهرت رسالة
3. **التحديث التلقائي**: سيبدأ جلب البيانات تلقائياً

### الاستخدام اليومي:
1. **عرض العملات**: ستظهر قائمة العملات الحلال
2. **التحديث**: انقر "تحديث الكل" للبيانات الجديدة
3. **التحليل**: انقر مزدوجاً على أي عملة للتحليل المفصل
4. **الفلترة**: استخدم "إشارات الشراء فقط"
5. **التصدير**: من قائمة "ملف" > "تصدير البيانات"

## 🔔 الإشعارات:

- **أيقونة النظام**: ستظهر في شريط المهام
- **إشعارات تلقائية**: عند ظهور إشارات جديدة
- **تخصيص الإعدادات**: انقر بالزر الأيمن على الأيقونة

## 📊 المؤشرات المتاحة:

| المؤشر | الوصف | الاستخدام |
|---------|--------|-----------|
| **RSI** | القوة النسبية | تحديد التشبع الشرائي/البيعي |
| **MACD** | تقارب المتوسطات | تحليل الاتجاهات |
| **MA** | المتوسطات المتحركة | تحديد الاتجاه العام |
| **الدعم/المقاومة** | مستويات السعر | نقاط الدخول والخروج |

## 🛠️ حل المشاكل:

### مشكلة: "التطبيق لا يبدأ"
```
✅ الحلول:
1. انقر بالزر الأيمن > "تشغيل كمسؤول"
2. تأكد من اتصال الإنترنت
3. أعد تشغيل الجهاز
4. تحقق من مساحة القرص الصلب
```

### مشكلة: "خطأ في الاتصال بـ Binance"
```
✅ الحلول:
1. تحقق من اتصال الإنترنت
2. انتظر دقيقة وأعد المحاولة
3. أعد تشغيل التطبيق
4. تحقق من إعدادات الجدار الناري
```

### مشكلة: "البيانات لا تتحدث"
```
✅ الحلول:
1. انقر "تحديث الكل"
2. تحقق من اتصال الإنترنت
3. انتظر انتهاء التحديث السابق
4. أعد تشغيل التطبيق
```

### مشكلة: "الإشعارات لا تعمل"
```
✅ الحلول:
1. تحقق من إعدادات الإشعارات في ويندوز
2. انقر بالزر الأيمن على أيقونة النظام
3. تأكد من تفعيل الإشعارات في التطبيق
```

## 📁 مجلدات البيانات:

```
📦 مجلد التطبيق
├── 🚀 HalalCryptoAnalyzer.exe
├── 📁 data/
│   └── 💾 crypto_analyzer.db    # قاعدة البيانات
├── 📁 logs/
│   └── 📄 app.log               # ملفات السجل
├── 📁 exports/
│   ├── 📊 تقرير_العملات.xlsx
│   ├── 📋 تحليل_البيانات.pdf
│   └── 📈 بيانات_التصدير.csv
└── 📁 charts/
    └── 📉 الرسوم_البيانية.png
```

## 🔄 التحديثات:

### للحصول على إصدار جديد:
1. **تحميل الإصدار الجديد**: احصل على `HalalCryptoAnalyzer.exe` الجديد
2. **النسخ الاحتياطي**: انسخ مجلد `data` (اختياري)
3. **الاستبدال**: استبدل الملف القديم بالجديد
4. **التشغيل**: شغل الإصدار الجديد

### البيانات المحفوظة:
- **قاعدة البيانات**: ستبقى محفوظة
- **الإعدادات**: ستبقى كما هي
- **التحليلات السابقة**: ستبقى متاحة

## ⚠️ تنويهات مهمة:

### الأمان:
- **التطبيق آمن**: لا يحتوي على فيروسات
- **البيانات محلية**: تُحفظ على جهازك فقط
- **لا توجد جمع بيانات**: لا نجمع معلوماتك الشخصية

### الاستثمار:
- **للتعليم فقط**: ليس نصيحة استثمارية
- **استشر خبير**: قبل أي قرار استثماري
- **المخاطر عالية**: العملات الرقمية متقلبة

### الأداء:
- **استهلاك الذاكرة**: 200-400 MB
- **استهلاك المعالج**: منخفض
- **استهلاك الإنترنت**: قليل (للتحديثات فقط)

## 📞 الدعم:

### للمساعدة:
- **راجع الدليل**: `README.md` للتفاصيل الكاملة
- **اختبر التطبيق**: شغل `test_exe.py` للتشخيص
- **تحقق من السجلات**: راجع مجلد `logs`

### للمطورين:
- **الكود المصدري**: متاح على GitHub
- **بناء الملف التنفيذي**: راجع `BUILD_EXE_GUIDE.md`
- **المساهمة**: مرحب بالمساهمات

---

## 🎉 استمتع بالتطبيق!

**محلل العملات الحلال** يساعدك في تحليل العملات الرقمية الحلال بطريقة احترافية وسهلة.

**تذكر**: هذا للتعليم فقط، وليس نصيحة استثمارية! 📚

---

**تم تطوير هذا التطبيق بواسطة AI Assistant - 2025** 🤖
