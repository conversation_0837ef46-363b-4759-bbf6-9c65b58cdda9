# -*- coding: utf-8 -*-
"""
قائمة العملات الرقمية الحلال المدعومة في Binance
تم اختيار هذه العملات بناءً على طبيعة مشاريعها وعدم تعارضها مع الأحكام الشرعية
"""

# قائمة العملات الحلال مع أزواج التداول بالدولار الأمريكي
HALAL_COINS = {
    'BTCUSDT': {
        'name': 'Bitcoin',
        'arabic_name': 'بيتكوين',
        'symbol': 'BTC',
        'category': 'عملة رقمية أساسية',
        'description': 'أول عملة رقمية لامركزية'
    },
    'ETHUSDT': {
        'name': 'Ethereum',
        'arabic_name': 'إيثيريوم',
        'symbol': 'ETH',
        'category': 'منصة عقود ذكية',
        'description': 'منصة للعقود الذكية والتطبيقات اللامركزية'
    },
    'ADAUSDT': {
        'name': 'Cardano',
        'arabic_name': 'كاردانو',
        'symbol': 'ADA',
        'category': 'منصة بلوك تشين',
        'description': 'منصة بلوك تشين مستدامة وقابلة للتطوير'
    },
    'DOTUSDT': {
        'name': 'Polkadot',
        'arabic_name': 'بولكادوت',
        'symbol': 'DOT',
        'category': 'شبكة متعددة السلاسل',
        'description': 'بروتوكول يربط بين شبكات البلوك تشين المختلفة'
    },
    'LINKUSDT': {
        'name': 'Chainlink',
        'arabic_name': 'تشين لينك',
        'symbol': 'LINK',
        'category': 'شبكة أوراكل',
        'description': 'شبكة أوراكل لامركزية تربط البلوك تشين بالعالم الحقيقي'
    },
    'LTCUSDT': {
        'name': 'Litecoin',
        'arabic_name': 'لايتكوين',
        'symbol': 'LTC',
        'category': 'عملة رقمية',
        'description': 'عملة رقمية سريعة ومنخفضة التكلفة'
    },
    'XLMUSDT': {
        'name': 'Stellar',
        'arabic_name': 'ستيلار',
        'symbol': 'XLM',
        'category': 'شبكة دفع',
        'description': 'شبكة دفع عالمية للتحويلات السريعة'
    },
    'VETUSD': {
        'name': 'VeChain',
        'arabic_name': 'في تشين',
        'symbol': 'VET',
        'category': 'سلسلة التوريد',
        'description': 'منصة بلوك تشين لإدارة سلسلة التوريد'
    },
    'ALGOUSDT': {
        'name': 'Algorand',
        'arabic_name': 'الجوراند',
        'symbol': 'ALGO',
        'category': 'منصة بلوك تشين',
        'description': 'منصة بلوك تشين سريعة وآمنة'
    },
    'ATOMUSDT': {
        'name': 'Cosmos',
        'arabic_name': 'كوزموس',
        'symbol': 'ATOM',
        'category': 'شبكة بلوك تشين',
        'description': 'شبكة من البلوك تشين المترابطة'
    },
    'XTZUSDT': {
        'name': 'Tezos',
        'arabic_name': 'تيزوس',
        'symbol': 'XTZ',
        'category': 'منصة عقود ذكية',
        'description': 'منصة عقود ذكية قابلة للترقية'
    },
    'FILUSDT': {
        'name': 'Filecoin',
        'arabic_name': 'فايل كوين',
        'symbol': 'FIL',
        'category': 'تخزين لامركزي',
        'description': 'شبكة تخزين لامركزية'
    },
    'ICPUSDT': {
        'name': 'Internet Computer',
        'arabic_name': 'إنترنت كمبيوتر',
        'symbol': 'ICP',
        'category': 'حوسبة لامركزية',
        'description': 'منصة حوسبة لامركزية'
    },
    'NEARUSDT': {
        'name': 'NEAR Protocol',
        'arabic_name': 'نير بروتوكول',
        'symbol': 'NEAR',
        'category': 'منصة تطوير',
        'description': 'منصة تطوير تطبيقات لامركزية'
    },
    'SOLUSDT': {
        'name': 'Solana',
        'arabic_name': 'سولانا',
        'symbol': 'SOL',
        'category': 'منصة عقود ذكية',
        'description': 'منصة عقود ذكية عالية الأداء'
    }
}

# قائمة الرموز فقط للاستخدام السريع
HALAL_SYMBOLS = list(HALAL_COINS.keys())

def get_coin_info(symbol):
    """
    الحصول على معلومات العملة
    
    Args:
        symbol (str): رمز العملة
        
    Returns:
        dict: معلومات العملة أو None إذا لم توجد
    """
    return HALAL_COINS.get(symbol)

def is_halal_coin(symbol):
    """
    التحقق من كون العملة حلال
    
    Args:
        symbol (str): رمز العملة
        
    Returns:
        bool: True إذا كانت العملة حلال
    """
    return symbol in HALAL_COINS

def get_all_halal_coins():
    """
    الحصول على جميع العملات الحلال
    
    Returns:
        dict: قاموس جميع العملات الحلال
    """
    return HALAL_COINS.copy()

def get_halal_symbols():
    """
    الحصول على رموز العملات الحلال فقط
    
    Returns:
        list: قائمة برموز العملات الحلال
    """
    return HALAL_SYMBOLS.copy()
