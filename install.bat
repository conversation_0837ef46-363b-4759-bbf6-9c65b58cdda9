@echo off
chcp 65001 >nul
title تثبيت محلل العملات الحلال

echo.
echo ========================================
echo تثبيت محلل العملات الحلال
echo Halal Crypto Analyzer Installer
echo ========================================
echo.

REM إنشاء مجلد التثبيت
set "INSTALL_DIR=%USERPROFILE%\HalalCryptoAnalyzer"

if not exist "%INSTALL_DIR%" (
    mkdir "%INSTALL_DIR%"
    echo ✅ تم إنشاء مجلد التثبيت: %INSTALL_DIR%
)

REM نسخ الملف التنفيذي
if exist "HalalCryptoAnalyzer.exe" (
    copy "HalalCryptoAnalyzer.exe" "%INSTALL_DIR%\"
    echo ✅ تم نسخ التطبيق إلى مجلد التثبيت
) else (
    echo ❌ لم يتم العثور على ملف HalalCryptoAnalyzer.exe
    pause
    exit /b 1
)

REM إنشاء اختصار على سطح المكتب
set "DESKTOP=%USERPROFILE%\Desktop"
set "SHORTCUT=%DESKTOP%\محلل العملات الحلال.lnk"

powershell -Command "$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%SHORTCUT%'); $Shortcut.TargetPath = '%INSTALL_DIR%\HalalCryptoAnalyzer.exe'; $Shortcut.WorkingDirectory = '%INSTALL_DIR%'; $Shortcut.Description = 'محلل العملات الحلال'; $Shortcut.Save()"

if exist "%SHORTCUT%" (
    echo ✅ تم إنشاء اختصار على سطح المكتب
) else (
    echo ⚠️ لم يتم إنشاء اختصار على سطح المكتب
)

echo.
echo ✅ تم التثبيت بنجاح!
echo 📁 مجلد التثبيت: %INSTALL_DIR%
echo 🖥️ اختصار سطح المكتب: %SHORTCUT%
echo.
echo يمكنك الآن تشغيل التطبيق من سطح المكتب أو من مجلد التثبيت
echo.

pause
