#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تثبيت ذكي للتطبيق مع اختيار أفضل مكتبة Qt
"""

import sys
import subprocess
import os

def test_qt_library(library_name, install_command):
    """اختبار مكتبة Qt"""
    print(f"🧪 اختبار {library_name}...")
    
    try:
        # محاولة الاستيراد
        if library_name == "PyQt6":
            from PyQt6.QtWidgets import QApplication
        elif library_name == "PyQt5":
            from PyQt5.QtWidgets import QApplication
        elif library_name == "PySide6":
            from PySide6.QtWidgets import QApplication
        
        print(f"✅ {library_name} يعمل بشكل صحيح")
        return True
        
    except ImportError:
        print(f"❌ {library_name} غير متاح")
        
        # محاولة التثبيت
        response = input(f"هل تريد تثبيت {library_name}؟ (y/n): ")
        if response.lower() in ['y', 'yes', 'نعم']:
            try:
                print(f"🔄 تثبيت {library_name}...")
                subprocess.check_call(install_command)
                
                # اختبار مرة أخرى
                if library_name == "PyQt6":
                    from PyQt6.QtWidgets import QApplication
                elif library_name == "PyQt5":
                    from PyQt5.QtWidgets import QApplication
                elif library_name == "PySide6":
                    from PySide6.QtWidgets import QApplication
                
                print(f"✅ تم تثبيت {library_name} بنجاح")
                return True
                
            except Exception as e:
                print(f"❌ فشل في تثبيت {library_name}: {e}")
                return False
        
        return False
    
    except Exception as e:
        print(f"❌ خطأ في اختبار {library_name}: {e}")
        return False

def install_base_requirements():
    """تثبيت المتطلبات الأساسية"""
    print("📦 تثبيت المتطلبات الأساسية...")
    
    base_packages = [
        "requests==2.31.0",
        "pandas==2.1.4", 
        "matplotlib==3.8.2",
        "numpy==1.26.2",
        "ta==0.10.2",
        "python-binance==1.0.19",
        "reportlab==4.0.7",
        "openpyxl==3.1.2",
        "pillow==10.1.0"
    ]
    
    for package in base_packages:
        try:
            print(f"تثبيت {package}...")
            subprocess.check_call([sys.executable, "-m", "pip", "install", package])
        except subprocess.CalledProcessError:
            print(f"⚠️ فشل في تثبيت {package}")
    
    print("✅ تم تثبيت المتطلبات الأساسية")

def main():
    """الدالة الرئيسية"""
    print("🚀 التثبيت الذكي لمحلل العملات الحلال")
    print("=" * 50)
    
    # تثبيت المتطلبات الأساسية أولاً
    install_base_requirements()
    
    print("\n🔍 البحث عن أفضل مكتبة Qt...")
    
    # قائمة مكتبات Qt مرتبة حسب الأولوية
    qt_libraries = [
        ("PyQt5", [sys.executable, "-m", "pip", "install", "PyQt5==5.15.10"]),
        ("PyQt6", [sys.executable, "-m", "pip", "install", "PyQt6==6.6.1"]),
        ("PySide6", [sys.executable, "-m", "pip", "install", "PySide6==6.6.1"])
    ]
    
    qt_installed = False
    
    for lib_name, install_cmd in qt_libraries:
        if test_qt_library(lib_name, install_cmd):
            print(f"\n🎉 سيتم استخدام {lib_name}")
            qt_installed = True
            break
    
    if not qt_installed:
        print("\n❌ فشل في تثبيت أي مكتبة Qt")
        print("\n🛠️ حلول يدوية:")
        print("1. تثبيت Visual C++ Redistributable:")
        print("   https://aka.ms/vs/17/release/vc_redist.x64.exe")
        print("2. تحديث ويندوز")
        print("3. استخدام Anaconda:")
        print("   conda install pyqt")
        return False
    
    # اختبار التطبيق
    print("\n🧪 اختبار التطبيق...")
    try:
        # استيراد الوحدات الأساسية
        import config
        from utils.halal_coins import get_halal_symbols
        from api.binance_client import BinanceClient
        from analysis.technical_analysis import TechnicalAnalyzer
        
        print("✅ جميع الوحدات تعمل بشكل صحيح")
        
        print("\n🎯 التطبيق جاهز للتشغيل!")
        print("استخدم أحد الأوامر التالية:")
        print("- python main.py")
        print("- python main_pyqt5.py")
        print("- run.bat")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار التطبيق: {e}")
        return False

if __name__ == "__main__":
    success = main()
    input("\nاضغط Enter للخروج...")
    sys.exit(0 if success else 1)
