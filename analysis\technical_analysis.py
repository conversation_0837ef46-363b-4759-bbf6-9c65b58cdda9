# -*- coding: utf-8 -*-
"""
وحدة التحليل الفني للعملات الرقمية
تحتوي على المؤشرات الفنية والتوصيات الذكية
"""

import pandas as pd
import numpy as np
import ta
from typing import Dict, Tuple, Optional
import config

class TechnicalAnalyzer:
    """كلاس التحليل الفني للعملات الرقمية"""
    
    def __init__(self):
        """تهيئة المحلل الفني"""
        self.rsi_period = config.RSI_PERIOD
        self.macd_fast = config.MACD_FAST
        self.macd_slow = config.MACD_SLOW
        self.macd_signal = config.MACD_SIGNAL
        self.ma_short = config.MA_SHORT
        self.ma_long = config.MA_LONG
        self.rsi_oversold = config.RSI_OVERSOLD
        self.rsi_overbought = config.RSI_OVERBOUGHT
    
    def calculate_rsi(self, data: pd.DataFrame) -> pd.Series:
        """
        حساب مؤشر القوة النسبية RSI
        
        Args:
            data (pd.DataFrame): بيانات الأسعار
            
        Returns:
            pd.Series: قيم RSI
        """
        return ta.momentum.RSIIndicator(
            close=data['close'], 
            window=self.rsi_period
        ).rsi()
    
    def calculate_macd(self, data: pd.DataFrame) -> Dict[str, pd.Series]:
        """
        حساب مؤشر MACD
        
        Args:
            data (pd.DataFrame): بيانات الأسعار
            
        Returns:
            Dict[str, pd.Series]: قيم MACD والإشارة والهيستوجرام
        """
        macd_indicator = ta.trend.MACD(
            close=data['close'],
            window_fast=self.macd_fast,
            window_slow=self.macd_slow,
            window_sign=self.macd_signal
        )
        
        return {
            'macd': macd_indicator.macd(),
            'signal': macd_indicator.macd_signal(),
            'histogram': macd_indicator.macd_diff()
        }
    
    def calculate_moving_averages(self, data: pd.DataFrame) -> Dict[str, pd.Series]:
        """
        حساب المتوسطات المتحركة
        
        Args:
            data (pd.DataFrame): بيانات الأسعار
            
        Returns:
            Dict[str, pd.Series]: المتوسطات المتحركة
        """
        return {
            'sma_short': ta.trend.SMAIndicator(
                close=data['close'], 
                window=self.ma_short
            ).sma_indicator(),
            'sma_long': ta.trend.SMAIndicator(
                close=data['close'], 
                window=self.ma_long
            ).sma_indicator(),
            'ema_short': ta.trend.EMAIndicator(
                close=data['close'], 
                window=self.ma_short
            ).ema_indicator(),
            'ema_long': ta.trend.EMAIndicator(
                close=data['close'], 
                window=self.ma_long
            ).ema_indicator()
        }
    
    def calculate_bollinger_bands(self, data: pd.DataFrame, window: int = 20) -> Dict[str, pd.Series]:
        """
        حساب نطاقات بولينجر
        
        Args:
            data (pd.DataFrame): بيانات الأسعار
            window (int): فترة الحساب
            
        Returns:
            Dict[str, pd.Series]: نطاقات بولينجر
        """
        bb_indicator = ta.volatility.BollingerBands(
            close=data['close'],
            window=window
        )
        
        return {
            'bb_upper': bb_indicator.bollinger_hband(),
            'bb_middle': bb_indicator.bollinger_mavg(),
            'bb_lower': bb_indicator.bollinger_lband(),
            'bb_width': bb_indicator.bollinger_wband(),
            'bb_percent': bb_indicator.bollinger_pband()
        }
    
    def calculate_volume_indicators(self, data: pd.DataFrame) -> Dict[str, pd.Series]:
        """
        حساب مؤشرات الحجم
        
        Args:
            data (pd.DataFrame): بيانات الأسعار والحجم
            
        Returns:
            Dict[str, pd.Series]: مؤشرات الحجم
        """
        return {
            'volume_sma': ta.volume.VolumeSMAIndicator(
                close=data['close'],
                volume=data['volume'],
                window=20
            ).volume_sma(),
            'obv': ta.volume.OnBalanceVolumeIndicator(
                close=data['close'],
                volume=data['volume']
            ).on_balance_volume()
        }
    
    def analyze_trend(self, data: pd.DataFrame) -> str:
        """
        تحليل الاتجاه العام
        
        Args:
            data (pd.DataFrame): بيانات الأسعار
            
        Returns:
            str: الاتجاه (صاعد/هابط/جانبي)
        """
        mas = self.calculate_moving_averages(data)
        current_price = data['close'].iloc[-1]
        sma_short = mas['sma_short'].iloc[-1]
        sma_long = mas['sma_long'].iloc[-1]
        
        if current_price > sma_short > sma_long:
            return "صاعد"
        elif current_price < sma_short < sma_long:
            return "هابط"
        else:
            return "جانبي"
    
    def generate_signals(self, data: pd.DataFrame) -> Dict[str, str]:
        """
        توليد إشارات التداول
        
        Args:
            data (pd.DataFrame): بيانات الأسعار
            
        Returns:
            Dict[str, str]: إشارات التداول
        """
        # حساب المؤشرات
        rsi = self.calculate_rsi(data)
        macd_data = self.calculate_macd(data)
        mas = self.calculate_moving_averages(data)
        
        # القيم الحالية
        current_rsi = rsi.iloc[-1] if not rsi.empty else 50
        current_macd = macd_data['macd'].iloc[-1] if not macd_data['macd'].empty else 0
        current_signal = macd_data['signal'].iloc[-1] if not macd_data['signal'].empty else 0
        current_price = data['close'].iloc[-1]
        sma_short = mas['sma_short'].iloc[-1] if not mas['sma_short'].empty else current_price
        
        signals = {
            'rsi_signal': 'محايد',
            'macd_signal': 'محايد',
            'ma_signal': 'محايد',
            'overall_signal': 'محايد'
        }
        
        # إشارة RSI
        if current_rsi < self.rsi_oversold:
            signals['rsi_signal'] = 'شراء'
        elif current_rsi > self.rsi_overbought:
            signals['rsi_signal'] = 'بيع'
        
        # إشارة MACD
        if current_macd > current_signal:
            signals['macd_signal'] = 'شراء'
        elif current_macd < current_signal:
            signals['macd_signal'] = 'بيع'
        
        # إشارة المتوسط المتحرك
        if current_price > sma_short:
            signals['ma_signal'] = 'شراء'
        elif current_price < sma_short:
            signals['ma_signal'] = 'بيع'
        
        # الإشارة الإجمالية
        buy_signals = sum(1 for signal in signals.values() if signal == 'شراء')
        sell_signals = sum(1 for signal in signals.values() if signal == 'بيع')
        
        if buy_signals >= 2:
            signals['overall_signal'] = 'شراء'
        elif sell_signals >= 2:
            signals['overall_signal'] = 'بيع'
        
        return signals
    
    def calculate_support_resistance(self, data: pd.DataFrame, window: int = 20) -> Dict[str, float]:
        """
        حساب مستويات الدعم والمقاومة
        
        Args:
            data (pd.DataFrame): بيانات الأسعار
            window (int): فترة الحساب
            
        Returns:
            Dict[str, float]: مستويات الدعم والمقاومة
        """
        highs = data['high'].rolling(window=window).max()
        lows = data['low'].rolling(window=window).min()
        
        return {
            'resistance': highs.iloc[-1] if not highs.empty else data['high'].iloc[-1],
            'support': lows.iloc[-1] if not lows.empty else data['low'].iloc[-1]
        }
    
    def comprehensive_analysis(self, data: pd.DataFrame) -> Dict:
        """
        تحليل شامل للعملة
        
        Args:
            data (pd.DataFrame): بيانات الأسعار
            
        Returns:
            Dict: تحليل شامل
        """
        if data.empty:
            return {}
        
        # حساب جميع المؤشرات
        rsi = self.calculate_rsi(data)
        macd_data = self.calculate_macd(data)
        mas = self.calculate_moving_averages(data)
        bb_data = self.calculate_bollinger_bands(data)
        volume_data = self.calculate_volume_indicators(data)
        
        # تحليل الاتجاه والإشارات
        trend = self.analyze_trend(data)
        signals = self.generate_signals(data)
        support_resistance = self.calculate_support_resistance(data)
        
        # القيم الحالية
        current_price = data['close'].iloc[-1]
        current_rsi = rsi.iloc[-1] if not rsi.empty else 50
        
        return {
            'current_price': current_price,
            'trend': trend,
            'signals': signals,
            'rsi': current_rsi,
            'macd': {
                'macd': macd_data['macd'].iloc[-1] if not macd_data['macd'].empty else 0,
                'signal': macd_data['signal'].iloc[-1] if not macd_data['signal'].empty else 0,
                'histogram': macd_data['histogram'].iloc[-1] if not macd_data['histogram'].empty else 0
            },
            'moving_averages': {
                'sma_short': mas['sma_short'].iloc[-1] if not mas['sma_short'].empty else current_price,
                'sma_long': mas['sma_long'].iloc[-1] if not mas['sma_long'].empty else current_price
            },
            'support_resistance': support_resistance,
            'bollinger_bands': {
                'upper': bb_data['bb_upper'].iloc[-1] if not bb_data['bb_upper'].empty else current_price,
                'middle': bb_data['bb_middle'].iloc[-1] if not bb_data['bb_middle'].empty else current_price,
                'lower': bb_data['bb_lower'].iloc[-1] if not bb_data['bb_lower'].empty else current_price
            }
        }
