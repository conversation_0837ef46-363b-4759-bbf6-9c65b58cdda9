#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار الملف التنفيذي
يتحقق من وجود وعمل الملف التنفيذي المُنشأ
"""

import os
import sys
import subprocess
import time
from pathlib import Path

def check_exe_exists():
    """التحقق من وجود الملف التنفيذي"""
    exe_path = Path("dist/HalalCryptoAnalyzer.exe")
    
    if exe_path.exists():
        size_mb = exe_path.stat().st_size / (1024 * 1024)
        print(f"✅ الملف التنفيذي موجود: {exe_path}")
        print(f"📏 حجم الملف: {size_mb:.1f} MB")
        return True
    else:
        print("❌ الملف التنفيذي غير موجود")
        print("يرجى تشغيل build_exe.bat أولاً لإنشاء الملف التنفيذي")
        return False

def test_exe_launch():
    """اختبار تشغيل الملف التنفيذي"""
    exe_path = "dist/HalalCryptoAnalyzer.exe"
    
    print("🚀 اختبار تشغيل الملف التنفيذي...")
    print("ملاحظة: سيتم إغلاق التطبيق تلقائياً بعد 10 ثوانٍ")
    
    try:
        # تشغيل الملف التنفيذي
        process = subprocess.Popen([exe_path])
        
        # انتظار 10 ثوانٍ
        time.sleep(10)
        
        # التحقق من حالة العملية
        if process.poll() is None:
            print("✅ التطبيق يعمل بنجاح")
            
            # إنهاء العملية
            process.terminate()
            time.sleep(2)
            
            if process.poll() is None:
                process.kill()
            
            print("✅ تم إغلاق التطبيق بنجاح")
            return True
        else:
            print(f"❌ التطبيق توقف مع كود الخروج: {process.returncode}")
            return False
            
    except FileNotFoundError:
        print("❌ لم يتم العثور على الملف التنفيذي")
        return False
    except Exception as e:
        print(f"❌ خطأ في تشغيل التطبيق: {e}")
        return False

def check_dependencies():
    """التحقق من الملفات والمجلدات المطلوبة"""
    print("🔍 التحقق من الملفات المطلوبة...")
    
    required_files = [
        "dist/HalalCryptoAnalyzer.exe",
        "install.bat"
    ]
    
    optional_files = [
        "icon.ico",
        "version_info.txt",
        "README.md",
        "QUICK_START.md",
        "BUILD_EXE_GUIDE.md"
    ]
    
    all_good = True
    
    # فحص الملفات المطلوبة
    for file_path in required_files:
        if os.path.exists(file_path):
            print(f"✅ {file_path}")
        else:
            print(f"❌ {file_path} - مفقود")
            all_good = False
    
    # فحص الملفات الاختيارية
    for file_path in optional_files:
        if os.path.exists(file_path):
            print(f"✅ {file_path}")
        else:
            print(f"⚠️ {file_path} - اختياري، غير موجود")
    
    return all_good

def test_installer():
    """اختبار سكريبت التثبيت"""
    print("🔧 اختبار سكريبت التثبيت...")
    
    if not os.path.exists("install.bat"):
        print("❌ سكريبت التثبيت غير موجود")
        return False
    
    # قراءة محتوى السكريبت
    try:
        with open("install.bat", "r", encoding="utf-8") as f:
            content = f.read()
        
        # التحقق من وجود الأوامر المطلوبة
        required_commands = [
            "HalalCryptoAnalyzer.exe",
            "USERPROFILE",
            "mkdir",
            "copy"
        ]
        
        for cmd in required_commands:
            if cmd in content:
                print(f"✅ يحتوي على: {cmd}")
            else:
                print(f"❌ لا يحتوي على: {cmd}")
                return False
        
        print("✅ سكريبت التثبيت يبدو صحيحاً")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في قراءة سكريبت التثبيت: {e}")
        return False

def generate_test_report():
    """إنشاء تقرير الاختبار"""
    print("\n" + "="*50)
    print("📊 تقرير اختبار الملف التنفيذي")
    print("="*50)
    
    tests = [
        ("وجود الملف التنفيذي", check_exe_exists),
        ("الملفات المطلوبة", check_dependencies),
        ("سكريبت التثبيت", test_installer),
        ("تشغيل التطبيق", test_exe_launch),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n🧪 اختبار: {test_name}")
        print("-" * 30)
        
        try:
            result = test_func()
            results.append((test_name, result))
            
            if result:
                print(f"✅ {test_name}: نجح")
            else:
                print(f"❌ {test_name}: فشل")
                
        except Exception as e:
            print(f"❌ {test_name}: خطأ - {e}")
            results.append((test_name, False))
    
    # ملخص النتائج
    print("\n" + "="*50)
    print("📋 ملخص النتائج")
    print("="*50)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ نجح" if result else "❌ فشل"
        print(f"{test_name}: {status}")
    
    print(f"\nالنتيجة الإجمالية: {passed}/{total} اختبار نجح")
    
    if passed == total:
        print("🎉 جميع الاختبارات نجحت! الملف التنفيذي جاهز للتوزيع")
        return True
    else:
        print("⚠️ بعض الاختبارات فشلت، يرجى مراجعة المشاكل أعلاه")
        return False

def show_usage_instructions():
    """عرض تعليمات الاستخدام"""
    print("\n" + "="*50)
    print("📖 تعليمات الاستخدام")
    print("="*50)
    
    print("""
🎯 كيفية استخدام الملف التنفيذي:

1️⃣ للمستخدمين العاديين:
   - شغل install.bat لتثبيت التطبيق
   - استخدم الاختصار من سطح المكتب

2️⃣ للتشغيل المباشر:
   - انقر مزدوجاً على dist/HalalCryptoAnalyzer.exe

3️⃣ للتوزيع:
   - انسخ dist/HalalCryptoAnalyzer.exe و install.bat
   - أرفق ملف README.md للتعليمات

⚠️ متطلبات النظام:
   - Windows 10/11
   - 4GB RAM كحد أدنى
   - اتصال إنترنت للبيانات المباشرة

📁 الملفات المهمة:
   - HalalCryptoAnalyzer.exe: الملف التنفيذي الرئيسي
   - install.bat: سكريبت التثبيت
   - README.md: دليل الاستخدام الكامل
   - BUILD_EXE_GUIDE.md: دليل بناء الملف التنفيذي
""")

def main():
    """الدالة الرئيسية"""
    print("🧪 اختبار الملف التنفيذي - محلل العملات الحلال")
    print("="*60)
    
    # تشغيل الاختبارات
    success = generate_test_report()
    
    # عرض تعليمات الاستخدام
    show_usage_instructions()
    
    # النتيجة النهائية
    if success:
        print("\n🎊 التطبيق جاهز للاستخدام والتوزيع!")
    else:
        print("\n🔧 يرجى إصلاح المشاكل المذكورة أعلاه")
    
    return success

if __name__ == "__main__":
    success = main()
    input("\nاضغط Enter للخروج...")
    sys.exit(0 if success else 1)
