#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إصلاح مشاكل PyQt6 وتثبيت البديل المناسب
"""

import sys
import subprocess
import os

def check_python_version():
    """التحقق من إصدار Python"""
    version = sys.version_info
    print(f"إصدار Python: {version.major}.{version.minor}.{version.micro}")
    
    if version < (3, 8):
        print("❌ خطأ: يتطلب Python 3.8 أو أحدث")
        return False
    
    return True

def uninstall_pyqt6():
    """إلغاء تثبيت PyQt6 المعطل"""
    print("🔄 إلغاء تثبيت PyQt6...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "uninstall", "PyQt6", "-y"])
        print("✅ تم إلغاء تثبيت PyQt6")
        return True
    except subprocess.CalledProcessError:
        print("⚠️ لم يتم العثور على PyQt6 أو فشل في الإلغاء")
        return False

def install_pyqt5():
    """تثبيت PyQt5 كبديل"""
    print("🔄 تثبيت PyQt5...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "PyQt5==5.15.10"])
        print("✅ تم تثبيت PyQt5 بنجاح")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ فشل في تثبيت PyQt5: {e}")
        return False

def install_pyside6():
    """تثبيت PySide6 كبديل آخر"""
    print("🔄 تثبيت PySide6...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "PySide6"])
        print("✅ تم تثبيت PySide6 بنجاح")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ فشل في تثبيت PySide6: {e}")
        return False

def install_visual_cpp_redistributable():
    """تعليمات تثبيت Visual C++ Redistributable"""
    print("\n📋 تثبيت Visual C++ Redistributable:")
    print("1. اذهب إلى: https://aka.ms/vs/17/release/vc_redist.x64.exe")
    print("2. حمل وثبت Visual C++ Redistributable")
    print("3. أعد تشغيل الجهاز")
    print("4. جرب تشغيل التطبيق مرة أخرى")

def test_qt_import():
    """اختبار استيراد Qt"""
    print("\n🧪 اختبار استيراد Qt...")
    
    # اختبار PyQt6
    try:
        from PyQt6.QtWidgets import QApplication
        print("✅ PyQt6 يعمل بشكل صحيح")
        return "PyQt6"
    except ImportError as e:
        print(f"❌ PyQt6 لا يعمل: {e}")
    
    # اختبار PyQt5
    try:
        from PyQt5.QtWidgets import QApplication
        print("✅ PyQt5 يعمل بشكل صحيح")
        return "PyQt5"
    except ImportError as e:
        print(f"❌ PyQt5 لا يعمل: {e}")
    
    # اختبار PySide6
    try:
        from PySide6.QtWidgets import QApplication
        print("✅ PySide6 يعمل بشكل صحيح")
        return "PySide6"
    except ImportError as e:
        print(f"❌ PySide6 لا يعمل: {e}")
    
    return None

def main():
    """الدالة الرئيسية"""
    print("🔧 إصلاح مشاكل PyQt")
    print("=" * 40)
    
    if not check_python_version():
        return False
    
    # اختبار الحالة الحالية
    current_qt = test_qt_import()
    
    if current_qt:
        print(f"\n✅ {current_qt} يعمل بالفعل!")
        return True
    
    print("\n🔄 محاولة إصلاح المشكلة...")
    
    # الحل 1: إعادة تثبيت PyQt6
    print("\n📦 الحل 1: إعادة تثبيت PyQt6")
    uninstall_pyqt6()
    
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "PyQt6==6.6.1"])
        print("✅ تم إعادة تثبيت PyQt6")
        
        if test_qt_import():
            print("🎉 تم حل المشكلة!")
            return True
    except:
        print("❌ فشل في إعادة تثبيت PyQt6")
    
    # الحل 2: تثبيت PyQt5
    print("\n📦 الحل 2: التبديل إلى PyQt5")
    if install_pyqt5():
        if test_qt_import():
            print("🎉 تم حل المشكلة باستخدام PyQt5!")
            return True
    
    # الحل 3: تثبيت PySide6
    print("\n📦 الحل 3: التبديل إلى PySide6")
    if install_pyside6():
        if test_qt_import():
            print("🎉 تم حل المشكلة باستخدام PySide6!")
            return True
    
    # إذا فشلت جميع الحلول
    print("\n❌ لم تنجح الحلول التلقائية")
    print("\n🛠️ حلول يدوية:")
    install_visual_cpp_redistributable()
    
    print("\n📋 حلول إضافية:")
    print("1. أعد تشغيل الجهاز")
    print("2. تحديث ويندوز")
    print("3. تثبيت Python من python.org مباشرة")
    print("4. استخدام Anaconda بدلاً من pip")
    
    return False

if __name__ == "__main__":
    success = main()
    input("\nاضغط Enter للخروج...")
    sys.exit(0 if success else 1)
