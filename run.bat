@echo off
chcp 65001 >nul
title محلل العملات الحلال - Binance Halal Crypto Analyzer

echo.
echo ========================================
echo محلل العملات الحلال
echo Binance Halal Crypto Analyzer
echo ========================================
echo.

REM التحقق من وجود Python
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ خطأ: Python غير مثبت أو غير موجود في PATH
    echo يرجى تثبيت Python 3.8 أو أحدث من https://python.org
    pause
    exit /b 1
)

echo ✅ تم العثور على Python
echo.

REM التحقق من وجود pip
python -m pip --version >nul 2>&1
if errorlevel 1 (
    echo ❌ خطأ: pip غير متاح
    pause
    exit /b 1
)

echo ✅ pip متاح
echo.

REM التحقق من وجود ملف المتطلبات
if not exist "requirements.txt" (
    echo ❌ خطأ: ملف requirements.txt غير موجود
    pause
    exit /b 1
)

echo 🔄 جاري تثبيت المتطلبات...
python -m pip install -r requirements.txt

if errorlevel 1 (
    echo.
    echo ❌ فشل في تثبيت بعض المتطلبات
    echo يرجى التحقق من اتصال الإنترنت وإعادة المحاولة
    pause
    exit /b 1
)

echo.
echo ✅ تم تثبيت المتطلبات بنجاح
echo.

REM إنشاء المجلدات المطلوبة
if not exist "data" mkdir data
if not exist "logs" mkdir logs
if not exist "charts" mkdir charts
if not exist "exports" mkdir exports

echo 📁 تم إنشاء المجلدات المطلوبة
echo.

echo 🚀 تشغيل التطبيق...
echo.

REM تشغيل التطبيق
python main.py

if errorlevel 1 (
    echo.
    echo ❌ حدث خطأ أثناء تشغيل التطبيق
    pause
)

echo.
echo تم إغلاق التطبيق
pause
