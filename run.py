#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ملف تشغيل سريع للتطبيق
يتحقق من المتطلبات ويشغل التطبيق
"""

import sys
import os
import subprocess
import importlib.util

def check_python_version():
    """التحقق من إصدار Python"""
    if sys.version_info < (3, 8):
        print("❌ خطأ: يتطلب Python 3.8 أو أحدث")
        print(f"الإصدار الحالي: {sys.version}")
        return False
    print(f"✅ Python {sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}")
    return True

def check_required_packages():
    """التحقق من المكتبات المطلوبة"""
    required_packages = [
        'PyQt6',
        'requests', 
        'pandas',
        'matplotlib',
        'numpy',
        'ta'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        if importlib.util.find_spec(package) is None:
            missing_packages.append(package)
        else:
            print(f"✅ {package}")
    
    if missing_packages:
        print(f"\n❌ المكتبات المفقودة: {', '.join(missing_packages)}")
        print("يرجى تثبيت المتطلبات باستخدام:")
        print("pip install -r requirements.txt")
        return False
    
    return True

def install_requirements():
    """تثبيت المتطلبات تلقائياً"""
    print("🔄 جاري تثبيت المتطلبات...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("✅ تم تثبيت المتطلبات بنجاح")
        return True
    except subprocess.CalledProcessError:
        print("❌ فشل في تثبيت المتطلبات")
        return False

def create_directories():
    """إنشاء المجلدات المطلوبة"""
    directories = ['data', 'logs', 'charts', 'exports']
    
    for directory in directories:
        if not os.path.exists(directory):
            os.makedirs(directory)
            print(f"📁 تم إنشاء مجلد: {directory}")

def main():
    """الدالة الرئيسية"""
    print("🚀 محلل العملات الحلال - Binance Halal Crypto Analyzer")
    print("=" * 60)
    
    # التحقق من إصدار Python
    if not check_python_version():
        input("اضغط Enter للخروج...")
        return
    
    print("\n🔍 فحص المتطلبات...")
    
    # التحقق من المكتبات
    if not check_required_packages():
        response = input("\nهل تريد تثبيت المتطلبات تلقائياً؟ (y/n): ")
        if response.lower() in ['y', 'yes', 'نعم']:
            if not install_requirements():
                input("اضغط Enter للخروج...")
                return
        else:
            input("اضغط Enter للخروج...")
            return
    
    # إنشاء المجلدات
    print("\n📁 إعداد المجلدات...")
    create_directories()
    
    # تشغيل التطبيق
    print("\n🎯 تشغيل التطبيق...")
    try:
        import main
        main.main()
    except ImportError as e:
        print(f"❌ خطأ في استيراد التطبيق: {e}")
        input("اضغط Enter للخروج...")
    except Exception as e:
        print(f"❌ خطأ في تشغيل التطبيق: {e}")
        input("اضغط Enter للخروج...")

if __name__ == "__main__":
    main()
