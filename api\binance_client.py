# -*- coding: utf-8 -*-
"""
وحدة الاتصال بـ Binance API
تتعامل مع جلب البيانات الحقيقية للعملات الرقمية
"""

import logging
from typing import Dict, List, Optional
import pandas as pd
from binance.client import Client
from binance.exceptions import BinanceAPIException, BinanceOrderException
import config
from utils.halal_coins import get_halal_symbols

class BinanceClient:
    """كلاس للتعامل مع Binance API"""
    
    def __init__(self, api_key: str = None, api_secret: str = None):
        """
        تهيئة العميل
        
        Args:
            api_key (str): مفتاح API (اختياري للبيانات العامة)
            api_secret (str): سر API (اختياري للبيانات العامة)
        """
        self.api_key = api_key or config.BINANCE_API_KEY
        self.api_secret = api_secret or config.BINANCE_API_SECRET
        
        try:
            # إنشاء عميل Binance
            if self.api_key and self.api_secret:
                self.client = Client(self.api_key, self.api_secret)
            else:
                # استخدام البيانات العامة فقط
                self.client = Client()
                
            # اختبار الاتصال
            self.client.ping()
            logging.info("تم الاتصال بـ Binance API بنجاح")
            
        except Exception as e:
            logging.error(f"خطأ في الاتصال بـ Binance API: {e}")
            self.client = None
    
    def get_current_prices(self, symbols: List[str] = None) -> Dict[str, float]:
        """
        الحصول على الأسعار الحالية للعملات
        
        Args:
            symbols (List[str]): قائمة رموز العملات (افتراضياً العملات الحلال)
            
        Returns:
            Dict[str, float]: قاموس الأسعار الحالية
        """
        if not self.client:
            return {}
            
        if symbols is None:
            symbols = get_halal_symbols()
            
        try:
            prices = {}
            tickers = self.client.get_all_tickers()
            
            for ticker in tickers:
                if ticker['symbol'] in symbols:
                    prices[ticker['symbol']] = float(ticker['price'])
                    
            return prices
            
        except BinanceAPIException as e:
            logging.error(f"خطأ في جلب الأسعار: {e}")
            return {}
    
    def get_24hr_ticker(self, symbol: str) -> Dict:
        """
        الحصول على إحصائيات 24 ساعة للعملة
        
        Args:
            symbol (str): رمز العملة
            
        Returns:
            Dict: إحصائيات 24 ساعة
        """
        if not self.client:
            return {}
            
        try:
            ticker = self.client.get_ticker(symbol=symbol)
            return {
                'symbol': ticker['symbol'],
                'price': float(ticker['lastPrice']),
                'change': float(ticker['priceChange']),
                'change_percent': float(ticker['priceChangePercent']),
                'high': float(ticker['highPrice']),
                'low': float(ticker['lowPrice']),
                'volume': float(ticker['volume']),
                'quote_volume': float(ticker['quoteVolume']),
                'open_time': ticker['openTime'],
                'close_time': ticker['closeTime']
            }
            
        except BinanceAPIException as e:
            logging.error(f"خطأ في جلب إحصائيات {symbol}: {e}")
            return {}
    
    def get_klines(self, symbol: str, interval: str = '1h', limit: int = 500) -> pd.DataFrame:
        """
        الحصول على بيانات الشموع اليابانية
        
        Args:
            symbol (str): رمز العملة
            interval (str): الفترة الزمنية (1m, 5m, 15m, 30m, 1h, 4h, 1d)
            limit (int): عدد الشموع المطلوبة
            
        Returns:
            pd.DataFrame: بيانات الشموع اليابانية
        """
        if not self.client:
            return pd.DataFrame()
            
        try:
            klines = self.client.get_klines(
                symbol=symbol,
                interval=interval,
                limit=limit
            )
            
            # تحويل البيانات إلى DataFrame
            df = pd.DataFrame(klines, columns=[
                'timestamp', 'open', 'high', 'low', 'close', 'volume',
                'close_time', 'quote_asset_volume', 'number_of_trades',
                'taker_buy_base_asset_volume', 'taker_buy_quote_asset_volume', 'ignore'
            ])
            
            # تحويل الأنواع
            numeric_columns = ['open', 'high', 'low', 'close', 'volume']
            for col in numeric_columns:
                df[col] = pd.to_numeric(df[col])
                
            # تحويل الوقت
            df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
            df.set_index('timestamp', inplace=True)
            
            return df[numeric_columns]
            
        except BinanceAPIException as e:
            logging.error(f"خطأ في جلب بيانات الشموع لـ {symbol}: {e}")
            return pd.DataFrame()
    
    def get_order_book(self, symbol: str, limit: int = 100) -> Dict:
        """
        الحصول على دفتر الطلبات
        
        Args:
            symbol (str): رمز العملة
            limit (int): عدد المستويات المطلوبة
            
        Returns:
            Dict: دفتر الطلبات
        """
        if not self.client:
            return {}
            
        try:
            order_book = self.client.get_order_book(symbol=symbol, limit=limit)
            return {
                'bids': [[float(price), float(qty)] for price, qty in order_book['bids']],
                'asks': [[float(price), float(qty)] for price, qty in order_book['asks']],
                'last_update_id': order_book['lastUpdateId']
            }
            
        except BinanceAPIException as e:
            logging.error(f"خطأ في جلب دفتر الطلبات لـ {symbol}: {e}")
            return {}
    
    def get_exchange_info(self) -> Dict:
        """
        الحصول على معلومات البورصة
        
        Returns:
            Dict: معلومات البورصة
        """
        if not self.client:
            return {}
            
        try:
            return self.client.get_exchange_info()
            
        except BinanceAPIException as e:
            logging.error(f"خطأ في جلب معلومات البورصة: {e}")
            return {}
    
    def is_connected(self) -> bool:
        """
        التحقق من حالة الاتصال
        
        Returns:
            bool: True إذا كان الاتصال نشطاً
        """
        if not self.client:
            return False
            
        try:
            self.client.ping()
            return True
        except:
            return False
