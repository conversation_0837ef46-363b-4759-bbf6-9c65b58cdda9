# -*- coding: utf-8 -*-
"""
نافذة التحليل المفصل للعملات
"""

from PyQt6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel, 
                             QTabWidget, QWidget, QTableWidget, QTableWidgetItem,
                             QTextEdit, QPushButton, QGroupBox, QGridLayout,
                             QProgressBar, QMessageBox)
from PyQt6.QtCore import QThread, pyqtSignal, Qt
from PyQt6.QtGui import QFont, QPixmap
import matplotlib.pyplot as plt
from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
from matplotlib.figure import Figure
try:
    import mplfinance as mpf
    MPLFINANCE_AVAILABLE = True
except ImportError:
    MPLFINANCE_AVAILABLE = False
    print("تحذير: mplfinance غير متاح، سيتم استخدام matplotlib الأساسي")
import pandas as pd
from datetime import datetime, timedelta
from utils.halal_coins import get_coin_info

class ChartUpdateThread(QThread):
    """خيط تحديث الرسوم البيانية"""
    
    chart_updated = pyqtSignal(object, object)  # data, analysis
    error_occurred = pyqtSignal(str)
    
    def __init__(self, symbol, binance_client, analyzer):
        super().__init__()
        self.symbol = symbol
        self.binance_client = binance_client
        self.analyzer = analyzer
    
    def run(self):
        """تشغيل خيط التحديث"""
        try:
            # جلب البيانات التاريخية
            data = self.binance_client.get_klines(self.symbol, '1h', 200)
            
            if not data.empty:
                # التحليل الشامل
                analysis = self.analyzer.comprehensive_analysis(data)
                self.chart_updated.emit(data, analysis)
            else:
                self.error_occurred.emit("لا توجد بيانات متاحة للعملة")
                
        except Exception as e:
            self.error_occurred.emit(str(e))

class CandlestickChart(FigureCanvas):
    """رسم الشموع اليابانية"""
    
    def __init__(self, parent=None):
        self.figure = Figure(figsize=(12, 8))
        super().__init__(self.figure)
        self.setParent(parent)
        
    def plot_candlestick(self, data, analysis):
        """رسم الشموع اليابانية مع المؤشرات"""
        self.figure.clear()
        
        if data.empty:
            return
        
        # إعداد البيانات لـ mplfinance
        data_copy = data.copy()
        data_copy.index = pd.to_datetime(data_copy.index)
        
        # حساب المؤشرات للرسم
        rsi = self.calculate_rsi_for_plot(data_copy)
        macd_data = self.calculate_macd_for_plot(data_copy)
        
        # إعداد المؤشرات الإضافية
        apds = []
        
        # إضافة RSI
        if not rsi.empty:
            apds.append(mpf.make_addplot(rsi, panel=1, color='purple', ylabel='RSI'))
        
        # إضافة MACD
        if not macd_data['macd'].empty:
            apds.append(mpf.make_addplot(macd_data['macd'], panel=2, color='blue', ylabel='MACD'))
            apds.append(mpf.make_addplot(macd_data['signal'], panel=2, color='red'))
        
        # رسم الشموع
        try:
            mpf.plot(data_copy, 
                    type='candle',
                    style='charles',
                    title=f'تحليل {self.symbol}',
                    ylabel='السعر (USDT)',
                    volume=True,
                    addplot=apds,
                    figsize=(12, 8),
                    tight_layout=True,
                    ax=self.figure.add_subplot(111))
            
            self.draw()
            
        except Exception as e:
            print(f"خطأ في رسم الشموع: {e}")
    
    def calculate_rsi_for_plot(self, data, period=14):
        """حساب RSI للرسم"""
        try:
            import ta
            return ta.momentum.RSIIndicator(close=data['close'], window=period).rsi()
        except:
            return pd.Series()
    
    def calculate_macd_for_plot(self, data):
        """حساب MACD للرسم"""
        try:
            import ta
            macd_indicator = ta.trend.MACD(close=data['close'])
            return {
                'macd': macd_indicator.macd(),
                'signal': macd_indicator.macd_signal()
            }
        except:
            return {'macd': pd.Series(), 'signal': pd.Series()}

class AnalysisWindow(QDialog):
    """نافذة التحليل المفصل"""
    
    def __init__(self, symbol, binance_client, analyzer, parent=None):
        super().__init__(parent)
        self.symbol = symbol
        self.binance_client = binance_client
        self.analyzer = analyzer
        self.coin_info = get_coin_info(symbol)
        
        self.setWindowTitle(f"التحليل المفصل - {self.coin_info.get('arabic_name', symbol)}")
        self.setGeometry(100, 100, 1000, 700)
        
        self.setup_ui()
        self.load_analysis()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        layout = QVBoxLayout(self)
        
        # معلومات العملة
        info_layout = QHBoxLayout()
        
        # معلومات أساسية
        info_group = QGroupBox("معلومات العملة")
        info_grid = QGridLayout(info_group)
        
        self.symbol_label = QLabel(f"الرمز: {self.coin_info.get('symbol', self.symbol)}")
        self.name_label = QLabel(f"الاسم: {self.coin_info.get('arabic_name', '')}")
        self.category_label = QLabel(f"الفئة: {self.coin_info.get('category', '')}")
        self.description_label = QLabel(f"الوصف: {self.coin_info.get('description', '')}")
        
        info_grid.addWidget(self.symbol_label, 0, 0)
        info_grid.addWidget(self.name_label, 0, 1)
        info_grid.addWidget(self.category_label, 1, 0)
        info_grid.addWidget(self.description_label, 1, 1)
        
        info_layout.addWidget(info_group)
        
        # الأسعار الحالية
        price_group = QGroupBox("الأسعار الحالية")
        price_grid = QGridLayout(price_group)
        
        self.current_price_label = QLabel("السعر الحالي: --")
        self.change_label = QLabel("التغيير: --")
        self.volume_label = QLabel("الحجم: --")
        self.market_cap_label = QLabel("القيمة السوقية: --")
        
        price_grid.addWidget(self.current_price_label, 0, 0)
        price_grid.addWidget(self.change_label, 0, 1)
        price_grid.addWidget(self.volume_label, 1, 0)
        price_grid.addWidget(self.market_cap_label, 1, 1)
        
        info_layout.addWidget(price_group)
        
        layout.addLayout(info_layout)
        
        # التبويبات
        self.tabs = QTabWidget()
        
        # تبويب الرسم البياني
        self.chart_tab = QWidget()
        self.setup_chart_tab()
        self.tabs.addTab(self.chart_tab, "الرسم البياني")
        
        # تبويب المؤشرات الفنية
        self.indicators_tab = QWidget()
        self.setup_indicators_tab()
        self.tabs.addTab(self.indicators_tab, "المؤشرات الفنية")
        
        # تبويب التوصيات
        self.recommendations_tab = QWidget()
        self.setup_recommendations_tab()
        self.tabs.addTab(self.recommendations_tab, "التوصيات")
        
        layout.addWidget(self.tabs)
        
        # أزرار التحكم
        buttons_layout = QHBoxLayout()
        
        self.refresh_btn = QPushButton("تحديث")
        self.refresh_btn.clicked.connect(self.load_analysis)
        
        self.export_btn = QPushButton("تصدير التحليل")
        self.export_btn.clicked.connect(self.export_analysis)
        
        self.close_btn = QPushButton("إغلاق")
        self.close_btn.clicked.connect(self.close)
        
        buttons_layout.addWidget(self.refresh_btn)
        buttons_layout.addWidget(self.export_btn)
        buttons_layout.addStretch()
        buttons_layout.addWidget(self.close_btn)
        
        layout.addLayout(buttons_layout)
        
        # شريط التقدم
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        layout.addWidget(self.progress_bar)
    
    def setup_chart_tab(self):
        """إعداد تبويب الرسم البياني"""
        layout = QVBoxLayout(self.chart_tab)
        
        # الرسم البياني
        self.chart = CandlestickChart()
        layout.addWidget(self.chart)
    
    def setup_indicators_tab(self):
        """إعداد تبويب المؤشرات الفنية"""
        layout = QVBoxLayout(self.indicators_tab)
        
        # جدول المؤشرات
        self.indicators_table = QTableWidget()
        self.indicators_table.setColumnCount(3)
        self.indicators_table.setHorizontalHeaderLabels(["المؤشر", "القيمة", "التفسير"])
        
        layout.addWidget(self.indicators_table)
    
    def setup_recommendations_tab(self):
        """إعداد تبويب التوصيات"""
        layout = QVBoxLayout(self.recommendations_tab)
        
        # التوصية الإجمالية
        overall_group = QGroupBox("التوصية الإجمالية")
        overall_layout = QVBoxLayout(overall_group)
        
        self.overall_recommendation = QLabel("التوصية: --")
        self.overall_recommendation.setFont(QFont("Arial", 16, QFont.Weight.Bold))
        overall_layout.addWidget(self.overall_recommendation)
        
        layout.addWidget(overall_group)
        
        # تفاصيل التوصيات
        details_group = QGroupBox("تفاصيل التوصيات")
        details_layout = QVBoxLayout(details_group)
        
        self.recommendations_text = QTextEdit()
        self.recommendations_text.setReadOnly(True)
        details_layout.addWidget(self.recommendations_text)
        
        layout.addWidget(details_group)
        
        # مستويات الدعم والمقاومة
        levels_group = QGroupBox("مستويات الدعم والمقاومة")
        levels_layout = QGridLayout(levels_group)
        
        self.support_label = QLabel("الدعم: --")
        self.resistance_label = QLabel("المقاومة: --")
        self.entry_point_label = QLabel("نقطة الدخول المقترحة: --")
        self.exit_point_label = QLabel("نقطة الخروج المقترحة: --")
        
        levels_layout.addWidget(self.support_label, 0, 0)
        levels_layout.addWidget(self.resistance_label, 0, 1)
        levels_layout.addWidget(self.entry_point_label, 1, 0)
        levels_layout.addWidget(self.exit_point_label, 1, 1)
        
        layout.addWidget(levels_group)
    
    def load_analysis(self):
        """تحميل التحليل"""
        self.progress_bar.setVisible(True)
        self.progress_bar.setRange(0, 0)
        
        # إنشاء خيط التحديث
        self.update_thread = ChartUpdateThread(
            self.symbol, 
            self.binance_client, 
            self.analyzer
        )
        
        self.update_thread.chart_updated.connect(self.on_analysis_loaded)
        self.update_thread.error_occurred.connect(self.on_error_occurred)
        self.update_thread.finished.connect(self.on_loading_finished)
        
        self.update_thread.start()
    
    def on_analysis_loaded(self, data, analysis):
        """معالج تحميل التحليل"""
        # تحديث الرسم البياني
        self.chart.plot_candlestick(data, analysis)
        
        # تحديث المؤشرات
        self.update_indicators(analysis)
        
        # تحديث التوصيات
        self.update_recommendations(analysis)
        
        # تحديث معلومات الأسعار
        self.update_price_info(analysis)
    
    def update_indicators(self, analysis):
        """تحديث جدول المؤشرات"""
        indicators_data = [
            ("RSI", f"{analysis.get('rsi', 0):.2f}", self.interpret_rsi(analysis.get('rsi', 0))),
            ("MACD", f"{analysis.get('macd', {}).get('macd', 0):.4f}", "مؤشر الاتجاه"),
            ("MACD Signal", f"{analysis.get('macd', {}).get('signal', 0):.4f}", "خط الإشارة"),
            ("SMA 20", f"{analysis.get('moving_averages', {}).get('sma_short', 0):.4f}", "متوسط متحرك قصير"),
            ("SMA 50", f"{analysis.get('moving_averages', {}).get('sma_long', 0):.4f}", "متوسط متحرك طويل"),
            ("الاتجاه", analysis.get('trend', 'غير محدد'), "الاتجاه العام للسعر"),
        ]
        
        self.indicators_table.setRowCount(len(indicators_data))
        
        for row, (indicator, value, interpretation) in enumerate(indicators_data):
            self.indicators_table.setItem(row, 0, QTableWidgetItem(indicator))
            self.indicators_table.setItem(row, 1, QTableWidgetItem(value))
            self.indicators_table.setItem(row, 2, QTableWidgetItem(interpretation))
    
    def update_recommendations(self, analysis):
        """تحديث التوصيات"""
        signals = analysis.get('signals', {})
        overall_signal = signals.get('overall_signal', 'محايد')
        
        # التوصية الإجمالية
        self.overall_recommendation.setText(f"التوصية: {overall_signal}")
        
        if overall_signal == 'شراء':
            self.overall_recommendation.setStyleSheet("color: green")
        elif overall_signal == 'بيع':
            self.overall_recommendation.setStyleSheet("color: red")
        else:
            self.overall_recommendation.setStyleSheet("color: blue")
        
        # تفاصيل التوصيات
        recommendations_text = f"""
        إشارة RSI: {signals.get('rsi_signal', 'محايد')}
        إشارة MACD: {signals.get('macd_signal', 'محايد')}
        إشارة المتوسط المتحرك: {signals.get('ma_signal', 'محايد')}
        
        الاتجاه العام: {analysis.get('trend', 'غير محدد')}
        
        تحليل مفصل:
        - RSI الحالي: {analysis.get('rsi', 0):.2f}
        - إذا كان RSI أقل من 30، فالعملة في منطقة تشبع بيعي (فرصة شراء)
        - إذا كان RSI أكبر من 70، فالعملة في منطقة تشبع شرائي (فرصة بيع)
        
        - MACD: {analysis.get('macd', {}).get('macd', 0):.4f}
        - إشارة MACD: {analysis.get('macd', {}).get('signal', 0):.4f}
        - عندما يكون MACD أعلى من خط الإشارة، فهذا مؤشر إيجابي
        """
        
        self.recommendations_text.setText(recommendations_text)
        
        # مستويات الدعم والمقاومة
        support_resistance = analysis.get('support_resistance', {})
        support = support_resistance.get('support', 0)
        resistance = support_resistance.get('resistance', 0)
        current_price = analysis.get('current_price', 0)
        
        self.support_label.setText(f"الدعم: ${support:.4f}")
        self.resistance_label.setText(f"المقاومة: ${resistance:.4f}")
        
        # اقتراح نقاط الدخول والخروج
        if overall_signal == 'شراء':
            entry_point = support * 1.01  # 1% فوق الدعم
            exit_point = resistance * 0.99  # 1% تحت المقاومة
        elif overall_signal == 'بيع':
            entry_point = resistance * 0.99  # 1% تحت المقاومة للبيع
            exit_point = support * 1.01  # 1% فوق الدعم للإغلاق
        else:
            entry_point = current_price
            exit_point = current_price
        
        self.entry_point_label.setText(f"نقطة الدخول المقترحة: ${entry_point:.4f}")
        self.exit_point_label.setText(f"نقطة الخروج المقترحة: ${exit_point:.4f}")
    
    def update_price_info(self, analysis):
        """تحديث معلومات الأسعار"""
        current_price = analysis.get('current_price', 0)
        self.current_price_label.setText(f"السعر الحالي: ${current_price:.4f}")
        
        # يمكن إضافة المزيد من المعلومات هنا
    
    def interpret_rsi(self, rsi_value):
        """تفسير قيمة RSI"""
        if rsi_value < 30:
            return "تشبع بيعي - فرصة شراء"
        elif rsi_value > 70:
            return "تشبع شرائي - فرصة بيع"
        else:
            return "منطقة محايدة"
    
    def on_error_occurred(self, error_message):
        """معالج الأخطاء"""
        QMessageBox.warning(self, "خطأ", f"حدث خطأ أثناء تحميل التحليل:\n{error_message}")
    
    def on_loading_finished(self):
        """معالج انتهاء التحميل"""
        self.progress_bar.setVisible(False)
    
    def export_analysis(self):
        """تصدير التحليل"""
        # TODO: تنفيذ تصدير التحليل
        QMessageBox.information(self, "تصدير", "سيتم تنفيذ هذه الميزة قريباً")
