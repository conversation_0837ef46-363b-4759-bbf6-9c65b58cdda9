# -*- coding: utf-8 -*-
"""
مدير التصدير للبيانات والتقارير
يدعم تصدير البيانات إلى Excel و PDF و CSV
"""

import pandas as pd
import json
from datetime import datetime
from typing import Dict, List
import os
from reportlab.lib.pagesizes import letter, A4
from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch
from reportlab.lib import colors
from reportlab.pdfbase import pdfmetrics
from reportlab.pdfbase.ttfonts import TTFont
import config
from utils.halal_coins import get_coin_info

class ExportManager:
    """مدير التصدير"""
    
    def __init__(self):
        """تهيئة مدير التصدير"""
        self.ensure_export_directory()
        self.setup_arabic_font()
    
    def ensure_export_directory(self):
        """التأكد من وجود مجلد التصدير"""
        export_dir = os.path.join(os.getcwd(), 'exports')
        if not os.path.exists(export_dir):
            os.makedirs(export_dir)
        self.export_dir = export_dir
    
    def setup_arabic_font(self):
        """إعداد الخط العربي للـ PDF"""
        try:
            # يمكن إضافة خط عربي هنا إذا كان متاحاً
            # pdfmetrics.registerFont(TTFont('Arabic', 'path/to/arabic/font.ttf'))
            self.arabic_font_available = False
        except:
            self.arabic_font_available = False
    
    def export_to_excel(self, analysis_results: Dict, filename: str = None) -> str:
        """
        تصدير البيانات إلى Excel
        
        Args:
            analysis_results (Dict): نتائج التحليل
            filename (str): اسم الملف (اختياري)
            
        Returns:
            str: مسار الملف المُصدَّر
        """
        if filename is None:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f'crypto_analysis_{timestamp}.xlsx'
        
        filepath = os.path.join(self.export_dir, filename)
        
        # إعداد البيانات للتصدير
        export_data = []
        
        for symbol, data in analysis_results.items():
            coin_info = get_coin_info(symbol)
            ticker = data.get('ticker', {})
            analysis = data.get('analysis', {})
            signals = analysis.get('signals', {})
            
            row = {
                'الرمز': coin_info.get('symbol', symbol) if coin_info else symbol,
                'الاسم العربي': coin_info.get('arabic_name', '') if coin_info else '',
                'الفئة': coin_info.get('category', '') if coin_info else '',
                'السعر الحالي': ticker.get('price', 0),
                'التغيير %': ticker.get('change_percent', 0),
                'الحجم': ticker.get('volume', 0),
                'أعلى سعر 24ساعة': ticker.get('high', 0),
                'أقل سعر 24ساعة': ticker.get('low', 0),
                'RSI': analysis.get('rsi', 0),
                'MACD': analysis.get('macd', {}).get('macd', 0),
                'MACD Signal': analysis.get('macd', {}).get('signal', 0),
                'المتوسط المتحرك القصير': analysis.get('moving_averages', {}).get('sma_short', 0),
                'المتوسط المتحرك الطويل': analysis.get('moving_averages', {}).get('sma_long', 0),
                'الاتجاه': analysis.get('trend', ''),
                'إشارة RSI': signals.get('rsi_signal', ''),
                'إشارة MACD': signals.get('macd_signal', ''),
                'إشارة المتوسط المتحرك': signals.get('ma_signal', ''),
                'التوصية الإجمالية': signals.get('overall_signal', ''),
                'مستوى الدعم': analysis.get('support_resistance', {}).get('support', 0),
                'مستوى المقاومة': analysis.get('support_resistance', {}).get('resistance', 0),
                'تاريخ التحليل': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }
            
            export_data.append(row)
        
        # إنشاء DataFrame وتصديره
        df = pd.DataFrame(export_data)
        
        with pd.ExcelWriter(filepath, engine='openpyxl') as writer:
            # الورقة الرئيسية
            df.to_excel(writer, sheet_name='تحليل العملات', index=False)
            
            # ورقة الإحصائيات
            stats_data = self.calculate_statistics(analysis_results)
            stats_df = pd.DataFrame([stats_data])
            stats_df.to_excel(writer, sheet_name='الإحصائيات', index=False)
            
            # ورقة إشارات الشراء
            buy_signals = df[df['التوصية الإجمالية'] == 'شراء']
            buy_signals.to_excel(writer, sheet_name='إشارات الشراء', index=False)
            
            # ورقة إشارات البيع
            sell_signals = df[df['التوصية الإجمالية'] == 'بيع']
            sell_signals.to_excel(writer, sheet_name='إشارات البيع', index=False)
        
        return filepath
    
    def export_to_csv(self, analysis_results: Dict, filename: str = None) -> str:
        """
        تصدير البيانات إلى CSV
        
        Args:
            analysis_results (Dict): نتائج التحليل
            filename (str): اسم الملف (اختياري)
            
        Returns:
            str: مسار الملف المُصدَّر
        """
        if filename is None:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f'crypto_analysis_{timestamp}.csv'
        
        filepath = os.path.join(self.export_dir, filename)
        
        # إعداد البيانات للتصدير (نفس منطق Excel)
        export_data = []
        
        for symbol, data in analysis_results.items():
            coin_info = get_coin_info(symbol)
            ticker = data.get('ticker', {})
            analysis = data.get('analysis', {})
            signals = analysis.get('signals', {})
            
            row = {
                'Symbol': coin_info.get('symbol', symbol) if coin_info else symbol,
                'Arabic_Name': coin_info.get('arabic_name', '') if coin_info else '',
                'Category': coin_info.get('category', '') if coin_info else '',
                'Current_Price': ticker.get('price', 0),
                'Change_Percent': ticker.get('change_percent', 0),
                'Volume': ticker.get('volume', 0),
                'High_24h': ticker.get('high', 0),
                'Low_24h': ticker.get('low', 0),
                'RSI': analysis.get('rsi', 0),
                'MACD': analysis.get('macd', {}).get('macd', 0),
                'MACD_Signal': analysis.get('macd', {}).get('signal', 0),
                'SMA_Short': analysis.get('moving_averages', {}).get('sma_short', 0),
                'SMA_Long': analysis.get('moving_averages', {}).get('sma_long', 0),
                'Trend': analysis.get('trend', ''),
                'RSI_Signal': signals.get('rsi_signal', ''),
                'MACD_Signal_Status': signals.get('macd_signal', ''),
                'MA_Signal': signals.get('ma_signal', ''),
                'Overall_Signal': signals.get('overall_signal', ''),
                'Support_Level': analysis.get('support_resistance', {}).get('support', 0),
                'Resistance_Level': analysis.get('support_resistance', {}).get('resistance', 0),
                'Analysis_Date': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }
            
            export_data.append(row)
        
        # إنشاء DataFrame وتصديره
        df = pd.DataFrame(export_data)
        df.to_csv(filepath, index=False, encoding='utf-8-sig')
        
        return filepath
    
    def export_to_pdf(self, analysis_results: Dict, filename: str = None) -> str:
        """
        تصدير التقرير إلى PDF
        
        Args:
            analysis_results (Dict): نتائج التحليل
            filename (str): اسم الملف (اختياري)
            
        Returns:
            str: مسار الملف المُصدَّر
        """
        if filename is None:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f'crypto_report_{timestamp}.pdf'
        
        filepath = os.path.join(self.export_dir, filename)
        
        # إنشاء المستند
        doc = SimpleDocTemplate(filepath, pagesize=A4)
        story = []
        
        # الأنماط
        styles = getSampleStyleSheet()
        title_style = ParagraphStyle(
            'CustomTitle',
            parent=styles['Heading1'],
            fontSize=18,
            spaceAfter=30,
            alignment=1  # وسط
        )
        
        heading_style = ParagraphStyle(
            'CustomHeading',
            parent=styles['Heading2'],
            fontSize=14,
            spaceAfter=12
        )
        
        # العنوان
        title = Paragraph("تقرير تحليل العملات الرقمية الحلال", title_style)
        story.append(title)
        story.append(Spacer(1, 12))
        
        # معلومات التقرير
        report_info = f"""
        تاريخ التقرير: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
        عدد العملات المحللة: {len(analysis_results)}
        المصدر: Binance API
        """
        
        info_para = Paragraph(report_info, styles['Normal'])
        story.append(info_para)
        story.append(Spacer(1, 20))
        
        # الإحصائيات العامة
        stats = self.calculate_statistics(analysis_results)
        stats_heading = Paragraph("الإحصائيات العامة", heading_style)
        story.append(stats_heading)
        
        stats_text = f"""
        إجمالي العملات: {stats['total_coins']}
        إشارات الشراء: {stats['buy_signals']} ({stats['buy_percentage']:.1f}%)
        إشارات البيع: {stats['sell_signals']} ({stats['sell_percentage']:.1f}%)
        إشارات محايدة: {stats['neutral_signals']} ({stats['neutral_percentage']:.1f}%)
        متوسط RSI: {stats['avg_rsi']:.1f}
        """
        
        stats_para = Paragraph(stats_text, styles['Normal'])
        story.append(stats_para)
        story.append(Spacer(1, 20))
        
        # جدول إشارات الشراء
        buy_signals = [(symbol, data) for symbol, data in analysis_results.items() 
                      if data.get('analysis', {}).get('signals', {}).get('overall_signal') == 'شراء']
        
        if buy_signals:
            buy_heading = Paragraph("إشارات الشراء", heading_style)
            story.append(buy_heading)
            
            buy_table_data = [['العملة', 'السعر', 'RSI', 'الاتجاه']]
            
            for symbol, data in buy_signals:
                coin_info = get_coin_info(symbol)
                ticker = data.get('ticker', {})
                analysis = data.get('analysis', {})
                
                buy_table_data.append([
                    coin_info.get('arabic_name', symbol) if coin_info else symbol,
                    f"${ticker.get('price', 0):.4f}",
                    f"{analysis.get('rsi', 0):.1f}",
                    analysis.get('trend', '')
                ])
            
            buy_table = Table(buy_table_data)
            buy_table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
                ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                ('FONTSIZE', (0, 0), (-1, 0), 12),
                ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
                ('GRID', (0, 0), (-1, -1), 1, colors.black)
            ]))
            
            story.append(buy_table)
            story.append(Spacer(1, 20))
        
        # جدول إشارات البيع
        sell_signals = [(symbol, data) for symbol, data in analysis_results.items() 
                       if data.get('analysis', {}).get('signals', {}).get('overall_signal') == 'بيع']
        
        if sell_signals:
            sell_heading = Paragraph("إشارات البيع", heading_style)
            story.append(sell_heading)
            
            sell_table_data = [['العملة', 'السعر', 'RSI', 'الاتجاه']]
            
            for symbol, data in sell_signals:
                coin_info = get_coin_info(symbol)
                ticker = data.get('ticker', {})
                analysis = data.get('analysis', {})
                
                sell_table_data.append([
                    coin_info.get('arabic_name', symbol) if coin_info else symbol,
                    f"${ticker.get('price', 0):.4f}",
                    f"{analysis.get('rsi', 0):.1f}",
                    analysis.get('trend', '')
                ])
            
            sell_table = Table(sell_table_data)
            sell_table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
                ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                ('FONTSIZE', (0, 0), (-1, 0), 12),
                ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                ('BACKGROUND', (0, 1), (-1, -1), colors.lightcoral),
                ('GRID', (0, 0), (-1, -1), 1, colors.black)
            ]))
            
            story.append(sell_table)
        
        # إضافة تنويه
        disclaimer = Paragraph(
            "تنويه: هذا التقرير للأغراض التعليمية فقط ولا يُعتبر نصيحة استثمارية. "
            "يُرجى إجراء البحث الخاص بك واستشارة مستشار مالي قبل اتخاذ أي قرارات استثمارية.",
            styles['Normal']
        )
        story.append(Spacer(1, 30))
        story.append(disclaimer)
        
        # بناء المستند
        doc.build(story)
        
        return filepath
    
    def export_to_json(self, analysis_results: Dict, filename: str = None) -> str:
        """
        تصدير البيانات إلى JSON
        
        Args:
            analysis_results (Dict): نتائج التحليل
            filename (str): اسم الملف (اختياري)
            
        Returns:
            str: مسار الملف المُصدَّر
        """
        if filename is None:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f'crypto_analysis_{timestamp}.json'
        
        filepath = os.path.join(self.export_dir, filename)
        
        # إعداد البيانات للتصدير
        export_data = {
            'export_info': {
                'timestamp': datetime.now().isoformat(),
                'total_coins': len(analysis_results),
                'source': 'Binance API',
                'analyzer': 'Halal Crypto Analyzer'
            },
            'statistics': self.calculate_statistics(analysis_results),
            'analysis_results': {}
        }
        
        for symbol, data in analysis_results.items():
            coin_info = get_coin_info(symbol)
            
            export_data['analysis_results'][symbol] = {
                'coin_info': coin_info,
                'ticker_data': data.get('ticker', {}),
                'technical_analysis': data.get('analysis', {}),
                'analysis_timestamp': datetime.now().isoformat()
            }
        
        # كتابة الملف
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(export_data, f, ensure_ascii=False, indent=2)
        
        return filepath
    
    def calculate_statistics(self, analysis_results: Dict) -> Dict:
        """
        حساب الإحصائيات العامة
        
        Args:
            analysis_results (Dict): نتائج التحليل
            
        Returns:
            Dict: الإحصائيات
        """
        total_coins = len(analysis_results)
        buy_signals = 0
        sell_signals = 0
        neutral_signals = 0
        rsi_values = []
        
        for data in analysis_results.values():
            analysis = data.get('analysis', {})
            signals = analysis.get('signals', {})
            overall_signal = signals.get('overall_signal', 'محايد')
            
            if overall_signal == 'شراء':
                buy_signals += 1
            elif overall_signal == 'بيع':
                sell_signals += 1
            else:
                neutral_signals += 1
            
            rsi = analysis.get('rsi', 0)
            if rsi > 0:
                rsi_values.append(rsi)
        
        avg_rsi = sum(rsi_values) / len(rsi_values) if rsi_values else 0
        
        return {
            'total_coins': total_coins,
            'buy_signals': buy_signals,
            'sell_signals': sell_signals,
            'neutral_signals': neutral_signals,
            'buy_percentage': (buy_signals / total_coins * 100) if total_coins > 0 else 0,
            'sell_percentage': (sell_signals / total_coins * 100) if total_coins > 0 else 0,
            'neutral_percentage': (neutral_signals / total_coins * 100) if total_coins > 0 else 0,
            'avg_rsi': avg_rsi
        }
