# ملف إعدادات البيئة النموذجي
# انسخ هذا الملف إلى .env وأدخل قيمك الخاصة

# إعدادات Binance API (اختيارية للبيانات العامة)
# يمكن الحصول عليها من https://www.binance.com/en/my/settings/api-management
BINANCE_API_KEY=your_api_key_here
BINANCE_API_SECRET=your_api_secret_here

# إعدادات قاعدة البيانات
DATABASE_PATH=data/crypto_analyzer.db

# إعدادات التحديث (بالثواني)
UPDATE_INTERVAL=60

# إعدادات المؤشرات الفنية
RSI_PERIOD=14
MACD_FAST=12
MACD_SLOW=26
MACD_SIGNAL=9
MA_SHORT=20
MA_LONG=50

# حدود المؤشرات
RSI_OVERSOLD=30
RSI_OVERBOUGHT=70

# إعدادات الواجهة
WINDOW_WIDTH=1200
WINDOW_HEIGHT=800
LANGUAGE=ar

# إعدادات السجلات
LOG_LEVEL=INFO
LOG_FILE=logs/app.log

# إعدادات الإشعارات
ENABLE_NOTIFICATIONS=true
ENABLE_SOUND=true
NOTIFICATION_DURATION=5000

# إعدادات التصدير
EXPORT_DIRECTORY=exports
DEFAULT_EXPORT_FORMAT=excel

# ملاحظات:
# - مفاتيح API اختيارية للبيانات العامة
# - للحصول على بيانات أكثر تفصيلاً، ستحتاج إلى مفاتيح API
# - تأكد من عدم مشاركة مفاتيح API مع أي شخص
# - احتفظ بهذا الملف آمناً ولا تضعه في نظام التحكم بالإصدارات
