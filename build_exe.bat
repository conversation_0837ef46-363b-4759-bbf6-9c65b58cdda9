@echo off
chcp 65001 >nul
title بناء ملف EXE - محلل العملات الحلال

echo.
echo ========================================
echo بناء ملف EXE - محلل العملات الحلال
echo Building EXE - Halal Crypto Analyzer
echo ========================================
echo.

REM التحقق من وجود Python
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ خطأ: Python غير مثبت أو غير موجود في PATH
    echo يرجى تثبيت Python 3.8 أو أحدث من https://python.org
    pause
    exit /b 1
)

echo ✅ تم العثور على Python
echo.

REM تثبيت المتطلبات إذا لم تكن مثبتة
echo 🔄 التحقق من المتطلبات...
python -c "import PyInstaller" 2>nul
if errorlevel 1 (
    echo 📦 تثبيت PyInstaller...
    python -m pip install pyinstaller
)

python -c "import PIL" 2>nul
if errorlevel 1 (
    echo 📦 تثبيت Pillow...
    python -m pip install pillow
)

echo ✅ المتطلبات جاهزة
echo.

REM تشغيل سكريبت البناء
echo 🔨 بدء بناء ملف EXE...
python build_exe.py

if errorlevel 1 (
    echo.
    echo ❌ فشل في بناء ملف EXE
    pause
    exit /b 1
)

echo.
echo ✅ تم بناء ملف EXE بنجاح!
echo.

REM التحقق من وجود الملف
if exist "dist\HalalCryptoAnalyzer.exe" (
    echo 📁 الملف موجود في: dist\HalalCryptoAnalyzer.exe
    
    REM حساب حجم الملف
    for %%A in ("dist\HalalCryptoAnalyzer.exe") do (
        set /a "size=%%~zA/1024/1024"
        echo 📏 حجم الملف: !size! MB تقريباً
    )
    
    echo.
    echo 🎯 الخطوات التالية:
    echo 1. شغل install.bat لتثبيت التطبيق
    echo 2. أو شغل dist\HalalCryptoAnalyzer.exe مباشرة
    echo 3. يمكن نسخ الملف إلى أي جهاز آخر بدون تثبيت Python
    
) else (
    echo ❌ لم يتم العثور على ملف EXE
)

echo.
pause
