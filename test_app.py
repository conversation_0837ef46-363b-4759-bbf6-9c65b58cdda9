#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار بسيط للتطبيق
يتحقق من عمل المكونات الأساسية
"""

import sys
import os
import unittest
from unittest.mock import Mock, patch
import pandas as pd

# إضافة المسار الحالي للمسارات
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

class TestHalalCoins(unittest.TestCase):
    """اختبار وحدة العملات الحلال"""
    
    def test_import_halal_coins(self):
        """اختبار استيراد وحدة العملات الحلال"""
        try:
            from utils.halal_coins import get_all_halal_coins, get_halal_symbols, is_halal_coin
            self.assertTrue(True)
        except ImportError as e:
            self.fail(f"فشل في استيراد وحدة العملات الحلال: {e}")
    
    def test_halal_coins_data(self):
        """اختبار بيانات العملات الحلال"""
        from utils.halal_coins import get_all_halal_coins, get_halal_symbols
        
        coins = get_all_halal_coins()
        symbols = get_halal_symbols()
        
        self.assertIsInstance(coins, dict)
        self.assertIsInstance(symbols, list)
        self.assertGreater(len(coins), 0)
        self.assertGreater(len(symbols), 0)
        
        # التحقق من وجود بيتكوين
        self.assertIn('BTCUSDT', symbols)

class TestTechnicalAnalysis(unittest.TestCase):
    """اختبار وحدة التحليل الفني"""
    
    def test_import_technical_analysis(self):
        """اختبار استيراد وحدة التحليل الفني"""
        try:
            from analysis.technical_analysis import TechnicalAnalyzer
            self.assertTrue(True)
        except ImportError as e:
            self.fail(f"فشل في استيراد وحدة التحليل الفني: {e}")
    
    def test_technical_analyzer_creation(self):
        """اختبار إنشاء محلل فني"""
        from analysis.technical_analysis import TechnicalAnalyzer
        
        analyzer = TechnicalAnalyzer()
        self.assertIsNotNone(analyzer)
        self.assertEqual(analyzer.rsi_period, 14)
    
    def test_rsi_calculation(self):
        """اختبار حساب RSI"""
        from analysis.technical_analysis import TechnicalAnalyzer
        
        # إنشاء بيانات وهمية
        data = pd.DataFrame({
            'close': [100, 102, 101, 103, 105, 104, 106, 108, 107, 109, 
                     111, 110, 112, 114, 113, 115, 117, 116, 118, 120]
        })
        
        analyzer = TechnicalAnalyzer()
        rsi = analyzer.calculate_rsi(data)
        
        self.assertIsInstance(rsi, pd.Series)
        self.assertGreater(len(rsi), 0)

class TestDatabaseManager(unittest.TestCase):
    """اختبار مدير قاعدة البيانات"""
    
    def test_import_database_manager(self):
        """اختبار استيراد مدير قاعدة البيانات"""
        try:
            from database.db_manager import DatabaseManager
            self.assertTrue(True)
        except ImportError as e:
            self.fail(f"فشل في استيراد مدير قاعدة البيانات: {e}")
    
    def test_database_creation(self):
        """اختبار إنشاء قاعدة البيانات"""
        from database.db_manager import DatabaseManager
        
        # استخدام قاعدة بيانات في الذاكرة للاختبار
        db_manager = DatabaseManager(':memory:')
        self.assertIsNotNone(db_manager)

class TestBinanceClient(unittest.TestCase):
    """اختبار عميل Binance"""
    
    def test_import_binance_client(self):
        """اختبار استيراد عميل Binance"""
        try:
            from api.binance_client import BinanceClient
            self.assertTrue(True)
        except ImportError as e:
            self.fail(f"فشل في استيراد عميل Binance: {e}")
    
    def test_binance_client_creation(self):
        """اختبار إنشاء عميل Binance"""
        from api.binance_client import BinanceClient
        
        # إنشاء عميل بدون مفاتيح API
        client = BinanceClient()
        self.assertIsNotNone(client)

class TestNotifications(unittest.TestCase):
    """اختبار نظام الإشعارات"""
    
    def test_import_notifications(self):
        """اختبار استيراد نظام الإشعارات"""
        try:
            from utils.notifications import NotificationManager
            self.assertTrue(True)
        except ImportError as e:
            self.fail(f"فشل في استيراد نظام الإشعارات: {e}")

class TestExportManager(unittest.TestCase):
    """اختبار مدير التصدير"""
    
    def test_import_export_manager(self):
        """اختبار استيراد مدير التصدير"""
        try:
            from utils.export_manager import ExportManager
            self.assertTrue(True)
        except ImportError as e:
            self.fail(f"فشل في استيراد مدير التصدير: {e}")

def run_basic_tests():
    """تشغيل الاختبارات الأساسية"""
    print("🧪 تشغيل الاختبارات الأساسية...")
    print("=" * 50)
    
    # اختبار استيراد المكتبات الأساسية
    required_modules = [
        ('PyQt6.QtWidgets', 'PyQt6'),
        ('pandas', 'pandas'),
        ('numpy', 'numpy'),
        ('requests', 'requests'),
        ('matplotlib', 'matplotlib')
    ]
    
    for module_name, display_name in required_modules:
        try:
            __import__(module_name)
            print(f"✅ {display_name}")
        except ImportError:
            print(f"❌ {display_name} - غير متاح")
    
    print("\n🔍 اختبار وحدات التطبيق...")
    
    # تشغيل اختبارات unittest
    loader = unittest.TestLoader()
    suite = unittest.TestSuite()
    
    # إضافة اختبارات
    test_classes = [
        TestHalalCoins,
        TestTechnicalAnalysis,
        TestDatabaseManager,
        TestBinanceClient,
        TestNotifications,
        TestExportManager
    ]
    
    for test_class in test_classes:
        tests = loader.loadTestsFromTestCase(test_class)
        suite.addTests(tests)
    
    # تشغيل الاختبارات
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    print("\n" + "=" * 50)
    if result.wasSuccessful():
        print("✅ جميع الاختبارات نجحت!")
        return True
    else:
        print(f"❌ فشل {len(result.failures + result.errors)} اختبار")
        return False

def test_gui_components():
    """اختبار مكونات الواجهة الرسومية"""
    print("\n🖥️ اختبار مكونات الواجهة الرسومية...")
    
    try:
        from PyQt6.QtWidgets import QApplication
        from gui.main_window import MainWindow
        
        # إنشاء تطبيق وهمي
        app = QApplication([])
        
        # محاولة إنشاء النافذة الرئيسية
        window = MainWindow()
        
        print("✅ تم إنشاء النافذة الرئيسية بنجاح")
        
        # إغلاق التطبيق
        app.quit()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء الواجهة الرسومية: {e}")
        return False

def main():
    """الدالة الرئيسية للاختبار"""
    print("🚀 اختبار تطبيق محلل العملات الحلال")
    print("=" * 60)
    
    # تشغيل الاختبارات الأساسية
    basic_tests_passed = run_basic_tests()
    
    # اختبار الواجهة الرسومية
    gui_tests_passed = test_gui_components()
    
    print("\n" + "=" * 60)
    print("📊 نتائج الاختبار:")
    print(f"الاختبارات الأساسية: {'✅ نجح' if basic_tests_passed else '❌ فشل'}")
    print(f"اختبار الواجهة: {'✅ نجح' if gui_tests_passed else '❌ فشل'}")
    
    if basic_tests_passed and gui_tests_passed:
        print("\n🎉 التطبيق جاهز للتشغيل!")
        return True
    else:
        print("\n⚠️ يوجد مشاكل تحتاج إلى إصلاح")
        return False

if __name__ == "__main__":
    success = main()
    if not success:
        sys.exit(1)
