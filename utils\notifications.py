# -*- coding: utf-8 -*-
"""
نظام الإشعارات للتطبيق
يتعامل مع إرسال الإشعارات عند ظهور إشارات مهمة
"""

import logging
from typing import Dict, List
from datetime import datetime
from PyQt6.QtWidgets import QSystemTrayIcon, QMenu, QApplication
from PyQt6.QtGui import QIcon, QPixmap, QAction
from PyQt6.QtCore import QObject, pyqtSignal, QTimer
from database.db_manager import DatabaseManager
from utils.halal_coins import get_coin_info

class NotificationManager(QObject):
    """مدير الإشعارات"""
    
    notification_triggered = pyqtSignal(str, str, str)  # title, message, icon_type
    
    def __init__(self, db_manager: DatabaseManager):
        super().__init__()
        self.db_manager = db_manager
        self.previous_signals = {}  # لتتبع الإشارات السابقة
        self.setup_system_tray()
        
    def setup_system_tray(self):
        """إعداد أيقونة النظام"""
        if QSystemTrayIcon.isSystemTrayAvailable():
            self.tray_icon = QSystemTrayIcon()
            
            # إنشاء أيقونة بسيطة (يمكن استبدالها بأيقونة مخصصة)
            pixmap = QPixmap(16, 16)
            pixmap.fill()
            icon = QIcon(pixmap)
            
            self.tray_icon.setIcon(icon)
            self.tray_icon.setToolTip("محلل العملات الحلال")
            
            # إعداد القائمة
            tray_menu = QMenu()
            
            show_action = QAction("عرض التطبيق", self)
            show_action.triggered.connect(self.show_main_window)
            tray_menu.addAction(show_action)
            
            tray_menu.addSeparator()
            
            quit_action = QAction("خروج", self)
            quit_action.triggered.connect(QApplication.quit)
            tray_menu.addAction(quit_action)
            
            self.tray_icon.setContextMenu(tray_menu)
            self.tray_icon.show()
            
            # ربط النقر على الأيقونة
            self.tray_icon.activated.connect(self.on_tray_icon_activated)
        else:
            self.tray_icon = None
            logging.warning("System tray غير متاح")
    
    def show_main_window(self):
        """عرض النافذة الرئيسية"""
        # سيتم ربطها بالنافذة الرئيسية لاحقاً
        pass
    
    def on_tray_icon_activated(self, reason):
        """معالج النقر على أيقونة النظام"""
        if reason == QSystemTrayIcon.ActivationReason.DoubleClick:
            self.show_main_window()
    
    def check_for_signals(self, analysis_results: Dict):
        """
        فحص الإشارات الجديدة وإرسال الإشعارات
        
        Args:
            analysis_results (Dict): نتائج التحليل للعملات
        """
        for symbol, data in analysis_results.items():
            analysis = data.get('analysis', {})
            signals = analysis.get('signals', {})
            overall_signal = signals.get('overall_signal', 'محايد')
            
            # التحقق من تغيير الإشارة
            previous_signal = self.previous_signals.get(symbol, 'محايد')
            
            if overall_signal != previous_signal and overall_signal != 'محايد':
                self.send_signal_notification(symbol, overall_signal, analysis)
                
                # حفظ الإشعار في قاعدة البيانات
                coin_info = get_coin_info(symbol)
                coin_name = coin_info.get('arabic_name', symbol) if coin_info else symbol
                
                message = f"إشارة {overall_signal} جديدة للعملة {coin_name}"
                current_price = analysis.get('current_price', 0)
                
                self.db_manager.save_notification(
                    symbol=symbol,
                    notification_type=overall_signal,
                    message=message,
                    price=current_price
                )
            
            # تحديث الإشارة السابقة
            self.previous_signals[symbol] = overall_signal
    
    def send_signal_notification(self, symbol: str, signal: str, analysis: Dict):
        """
        إرسال إشعار إشارة تداول
        
        Args:
            symbol (str): رمز العملة
            signal (str): نوع الإشارة (شراء/بيع)
            analysis (Dict): بيانات التحليل
        """
        coin_info = get_coin_info(symbol)
        coin_name = coin_info.get('arabic_name', symbol) if coin_info else symbol
        current_price = analysis.get('current_price', 0)
        rsi = analysis.get('rsi', 0)
        
        title = f"إشارة {signal} - {coin_name}"
        
        if signal == 'شراء':
            message = f"""
            إشارة شراء جديدة للعملة {coin_name}
            السعر الحالي: ${current_price:.4f}
            RSI: {rsi:.1f}
            
            يُنصح بمراجعة التحليل المفصل قبل اتخاذ أي قرار استثماري.
            """
            icon_type = "buy"
        else:  # بيع
            message = f"""
            إشارة بيع جديدة للعملة {coin_name}
            السعر الحالي: ${current_price:.4f}
            RSI: {rsi:.1f}
            
            يُنصح بمراجعة التحليل المفصل قبل اتخاذ أي قرار استثماري.
            """
            icon_type = "sell"
        
        # إرسال الإشعار
        self.show_notification(title, message, icon_type)
        
        # إرسال إشارة للواجهة
        self.notification_triggered.emit(title, message, icon_type)
    
    def show_notification(self, title: str, message: str, icon_type: str = "info"):
        """
        عرض إشعار النظام
        
        Args:
            title (str): عنوان الإشعار
            message (str): نص الإشعار
            icon_type (str): نوع الأيقونة
        """
        if self.tray_icon and self.tray_icon.isVisible():
            # تحديد نوع الأيقونة
            if icon_type == "buy":
                icon = QSystemTrayIcon.MessageIcon.Information
            elif icon_type == "sell":
                icon = QSystemTrayIcon.MessageIcon.Warning
            else:
                icon = QSystemTrayIcon.MessageIcon.Information
            
            # عرض الإشعار
            self.tray_icon.showMessage(
                title,
                message,
                icon,
                5000  # 5 ثوانٍ
            )
        else:
            # إذا لم تكن أيقونة النظام متاحة، استخدم الطباعة
            logging.info(f"إشعار: {title} - {message}")
    
    def send_price_alert(self, symbol: str, current_price: float, target_price: float, alert_type: str):
        """
        إرسال تنبيه سعر
        
        Args:
            symbol (str): رمز العملة
            current_price (float): السعر الحالي
            target_price (float): السعر المستهدف
            alert_type (str): نوع التنبيه (above/below)
        """
        coin_info = get_coin_info(symbol)
        coin_name = coin_info.get('arabic_name', symbol) if coin_info else symbol
        
        if alert_type == "above":
            title = f"تنبيه سعر - {coin_name}"
            message = f"وصل سعر {coin_name} إلى ${current_price:.4f} (أعلى من ${target_price:.4f})"
        else:
            title = f"تنبيه سعر - {coin_name}"
            message = f"انخفض سعر {coin_name} إلى ${current_price:.4f} (أقل من ${target_price:.4f})"
        
        self.show_notification(title, message, "price_alert")
        
        # حفظ في قاعدة البيانات
        self.db_manager.save_notification(
            symbol=symbol,
            notification_type="price_alert",
            message=message,
            price=current_price
        )
    
    def send_volume_alert(self, symbol: str, volume_change: float):
        """
        إرسال تنبيه حجم تداول
        
        Args:
            symbol (str): رمز العملة
            volume_change (float): نسبة تغيير الحجم
        """
        coin_info = get_coin_info(symbol)
        coin_name = coin_info.get('arabic_name', symbol) if coin_info else symbol
        
        title = f"تنبيه حجم - {coin_name}"
        message = f"ارتفع حجم تداول {coin_name} بنسبة {volume_change:.1f}%"
        
        self.show_notification(title, message, "volume_alert")
        
        # حفظ في قاعدة البيانات
        self.db_manager.save_notification(
            symbol=symbol,
            notification_type="volume_alert",
            message=message
        )
    
    def get_notification_history(self, limit: int = 50) -> List[Dict]:
        """
        الحصول على تاريخ الإشعارات
        
        Args:
            limit (int): عدد الإشعارات المطلوبة
            
        Returns:
            List[Dict]: قائمة الإشعارات
        """
        return self.db_manager.get_unread_notifications()[:limit]
    
    def mark_all_notifications_read(self):
        """تحديد جميع الإشعارات كمقروءة"""
        notifications = self.db_manager.get_unread_notifications()
        for notification in notifications:
            self.db_manager.mark_notification_read(notification['id'])
    
    def cleanup_old_notifications(self, days: int = 30):
        """
        تنظيف الإشعارات القديمة
        
        Args:
            days (int): عدد الأيام للاحتفاظ بالإشعارات
        """
        # TODO: تنفيذ تنظيف الإشعارات القديمة في قاعدة البيانات
        pass

class NotificationSettings:
    """إعدادات الإشعارات"""
    
    def __init__(self, db_manager: DatabaseManager):
        self.db_manager = db_manager
    
    def is_signal_notifications_enabled(self) -> bool:
        """التحقق من تفعيل إشعارات الإشارات"""
        return self.db_manager.get_setting('signal_notifications', 'true') == 'true'
    
    def set_signal_notifications(self, enabled: bool):
        """تفعيل/إلغاء تفعيل إشعارات الإشارات"""
        self.db_manager.save_setting('signal_notifications', 'true' if enabled else 'false')
    
    def is_price_alerts_enabled(self) -> bool:
        """التحقق من تفعيل تنبيهات الأسعار"""
        return self.db_manager.get_setting('price_alerts', 'true') == 'true'
    
    def set_price_alerts(self, enabled: bool):
        """تفعيل/إلغاء تفعيل تنبيهات الأسعار"""
        self.db_manager.save_setting('price_alerts', 'true' if enabled else 'false')
    
    def is_volume_alerts_enabled(self) -> bool:
        """التحقق من تفعيل تنبيهات الحجم"""
        return self.db_manager.get_setting('volume_alerts', 'false') == 'true'
    
    def set_volume_alerts(self, enabled: bool):
        """تفعيل/إلغاء تفعيل تنبيهات الحجم"""
        self.db_manager.save_setting('volume_alerts', 'true' if enabled else 'false')
    
    def get_notification_sound(self) -> bool:
        """الحصول على إعداد صوت الإشعارات"""
        return self.db_manager.get_setting('notification_sound', 'true') == 'true'
    
    def set_notification_sound(self, enabled: bool):
        """تفعيل/إلغاء تفعيل صوت الإشعارات"""
        self.db_manager.save_setting('notification_sound', 'true' if enabled else 'false')
