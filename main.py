#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
محلل العملات الحلال - Binance Halal Crypto Analyzer
تطبيق سطح مكتب لتحليل العملات الرقمية الحلال المدرجة في منصة Binance

المطور: AI Assistant
التاريخ: 2025-07-16
"""

import sys
import os

# محاولة استيراد Qt مع دعم إصدارات متعددة
try:
    from PyQt6.QtWidgets import QApplication
    from PyQt6.QtCore import QTranslator, QLocale
    from gui.main_window import MainWindow
    QT_VERSION = "PyQt6"
    print("✅ استخدام PyQt6")
except ImportError as e:
    try:
        from PyQt5.QtWidgets import QApplication
        from PyQt5.QtCore import QTranslator, QLocale
        from gui.main_window import MainWindow
        QT_VERSION = "PyQt5"
        print("✅ استخدام PyQt5")
    except ImportError:
        try:
            from PySide6.QtWidgets import QApplication
            from PySide6.QtCore import QTranslator, QLocale
            from gui.main_window import MainWindow
            QT_VERSION = "PySide6"
            print("✅ استخدام PySide6")
        except ImportError:
            print("❌ خطأ: لم يتم العثور على أي مكتبة Qt")
            print("\n🛠️ حلول سريعة:")
            print("1. شغل: python fix_pyqt.py")
            print("2. أو شغل: python install_smart.py")
            print("3. أو ثبت PyQt5: pip install PyQt5")
            print("\n📚 للمزيد من الحلول، راجع: PYQT_TROUBLESHOOTING.md")
            input("اضغط Enter للخروج...")
            sys.exit(1)

def main():
    """دالة تشغيل التطبيق الرئيسية"""
    print(f"🚀 تشغيل محلل العملات الحلال باستخدام {QT_VERSION}")

    try:
        app = QApplication(sys.argv)

        # إعداد اللغة العربية
        app.setLayoutDirection(app.layoutDirection())

        # إنشاء النافذة الرئيسية
        window = MainWindow()
        window.show()

        print("✅ تم تشغيل التطبيق بنجاح")

        # تشغيل التطبيق (دعم PyQt5 و PyQt6)
        if QT_VERSION == "PyQt5":
            sys.exit(app.exec_())
        else:
            sys.exit(app.exec())

    except Exception as e:
        print(f"❌ خطأ في تشغيل التطبيق: {e}")
        print("\n🛠️ حلول مقترحة:")
        print("1. شغل: python fix_pyqt.py")
        print("2. أو شغل: python main_pyqt5.py")
        print("3. راجع: PYQT_TROUBLESHOOTING.md")
        input("اضغط Enter للخروج...")
        sys.exit(1)

if __name__ == "__main__":
    main()
