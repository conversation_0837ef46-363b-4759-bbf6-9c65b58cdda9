#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
محلل العملات الحلال - Binance Halal Crypto Analyzer
تطبيق سطح مكتب لتحليل العملات الرقمية الحلال المدرجة في منصة Binance

المطور: AI Assistant
التاريخ: 2025-07-16
"""

import sys
import os
from PyQt6.QtWidgets import QApplication
from PyQt6.QtCore import QTranslator, QLocale
from gui.main_window import MainWindow

def main():
    """دالة تشغيل التطبيق الرئيسية"""
    app = QApplication(sys.argv)
    
    # إعداد اللغة العربية
    app.setLayoutDirection(app.layoutDirection())
    
    # إنشاء النافذة الرئيسية
    window = MainWindow()
    window.show()
    
    # تشغيل التطبيق
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
